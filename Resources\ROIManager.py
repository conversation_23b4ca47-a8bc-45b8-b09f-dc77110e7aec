"""
ROI管理器模块
提供ROI创建、管理和分析功能
"""

import slicer
import vtk
import qt
import numpy as np

def onCreateROI(widget):
    """创建ROI的便捷函数 - 自动创建在CT数据中心"""
    try:
        # 获取当前输入数据
        inputVolume = None
        if hasattr(widget, 'inputSelector') and widget.inputSelector.currentNode():
            inputVolume = widget.inputSelector.currentNode()

        if not inputVolume:
            print("⚠️ 未选择输入数据，无法创建ROI")
            return

        # 检查是否是头位调整后的数据
        volumeName = inputVolume.GetName()
        isHeadAdjusted = "HeadAdjusted" in volumeName or "FirstQuadrant" in volumeName

        if isHeadAdjusted:
            print("✓ 检测到头位调整后的数据，ROI将在调整后的坐标系统中创建")
        else:
            print("⚠️ 建议先进行头位调整，然后再创建ROI以确保坐标一致性")

        # 获取ROI参数
        roi_name = widget.roiNameEdit.text if hasattr(widget, 'roiNameEdit') else "ROI_1"
        roi_length = widget.roiLengthSpinBox.value if hasattr(widget, 'roiLengthSpinBox') else 50.0
        roi_width = widget.roiWidthSpinBox.value if hasattr(widget, 'roiWidthSpinBox') else 50.0
        roi_height = widget.roiHeightSpinBox.value if hasattr(widget, 'roiHeightSpinBox') else 50.0

        # 自动计算数据中心作为ROI位置
        bounds = [0] * 6
        inputVolume.GetBounds(bounds)
        center_x = (bounds[0] + bounds[1]) / 2
        center_y = (bounds[2] + bounds[3]) / 2
        center_z = (bounds[4] + bounds[5]) / 2

        print(f"CT数据边界: [{bounds[0]:.1f}, {bounds[1]:.1f}, {bounds[2]:.1f}, {bounds[3]:.1f}, {bounds[4]:.1f}, {bounds[5]:.1f}]")
        print(f"计算的中心点: ({center_x:.1f}, {center_y:.1f}, {center_z:.1f})")

        # 创建ROI节点
        roiNode = slicer.mrmlScene.AddNewNodeByClass("vtkMRMLMarkupsROINode")
        roiNode.SetName(roi_name)

        # 设置ROI尺寸和位置（自动居中）
        roiNode.SetXYZ([center_x, center_y, center_z])
        roiNode.SetRadiusXYZ([roi_length/2, roi_width/2, roi_height/2])

        # 设置显示属性
        displayNode = roiNode.GetDisplayNode()
        if displayNode:
            displayNode.SetColor(0.0, 1.0, 0.0)  # 绿色
            displayNode.SetOpacity(0.8)  # 增加不透明度
            displayNode.SetFillOpacity(0.2)  # 增加填充不透明度
            displayNode.SetVisibility(True)  # 确保可见
            displayNode.SetVisibility2D(True)  # 在切片中显示（新API）
            displayNode.SetSliceIntersectionThickness(2)  # 设置切片中的线条粗细

        # 确保ROI在所有视图中可见
        roiNode.SetDisplayVisibility(True)

        # 使用安全的视图刷新，保持头位调整
        safeRefreshViews(inputVolume)

        # 为新创建的ROI添加观察器，监听手动调整
        addROIObserver(roiNode, widget)

        print(f"✓ 成功创建ROI: {roi_name}，位置: ({center_x:.1f}, {center_y:.1f}, {center_z:.1f})")
        if isHeadAdjusted:
            print("  ✓ ROI已在头位调整后的坐标系统中创建，可以直接进行裁切")
        else:
            print("  ⚠️ 建议先进行头位调整，再重新创建ROI")
        print("  提示: 您可以在3D视图中手动调整ROI，尺寸会自动同步到界面")

    except Exception as e:
        print(f"❌ 创建ROI失败: {e}")

def setupROIObserver(widget):
    """设置ROI观察器，监听ROI变化并同步到UI"""
    try:
        # 获取所有ROI节点
        roiNodes = slicer.util.getNodesByClass("vtkMRMLMarkupsROINode")

        for roiNode in roiNodes:
            # 为每个ROI添加观察器
            roiNode.AddObserver(slicer.vtkMRMLMarkupsNode.PointModifiedEvent,
                              lambda caller, event, widget=widget: onROIModified(caller, widget))
            roiNode.AddObserver(slicer.vtkMRMLTransformableNode.TransformModifiedEvent,
                              lambda caller, event, widget=widget: onROIModified(caller, widget))

        print(f"✓ 已为 {len(roiNodes)} 个ROI设置观察器")

    except Exception as e:
        print(f"设置ROI观察器失败: {e}")

def onROIModified(roiNode, widget):
    """ROI被修改时的回调函数 - 同步到UI控件"""
    try:
        if not roiNode:
            return

        # 获取ROI的当前参数
        center = [0, 0, 0]
        roiNode.GetXYZ(center)

        radius = [0, 0, 0]
        roiNode.GetRadiusXYZ(radius)

        # 更新UI控件
        if hasattr(widget, 'roiNameEdit'):
            widget.roiNameEdit.setText(roiNode.GetName())

        if hasattr(widget, 'roiLengthSpinBox'):
            widget.roiLengthSpinBox.setValue(radius[0] * 2)  # 半径转直径

        if hasattr(widget, 'roiWidthSpinBox'):
            widget.roiWidthSpinBox.setValue(radius[1] * 2)

        if hasattr(widget, 'roiHeightSpinBox'):
            widget.roiHeightSpinBox.setValue(radius[2] * 2)

        print(f"✓ 已同步ROI '{roiNode.GetName()}' 的参数到UI")
        print(f"  尺寸: {radius[0]*2:.1f} x {radius[1]*2:.1f} x {radius[2]*2:.1f} mm")

    except Exception as e:
        print(f"同步ROI参数失败: {e}")

def addROIObserver(roiNode, widget):
    """为新创建的ROI添加观察器"""
    try:
        if roiNode and widget:
            # 添加观察器监听ROI变化
            roiNode.AddObserver(slicer.vtkMRMLMarkupsNode.PointModifiedEvent,
                              lambda caller, event: onROIModified(caller, widget))
            roiNode.AddObserver(slicer.vtkMRMLTransformableNode.TransformModifiedEvent,
                              lambda caller, event: onROIModified(caller, widget))

            print(f"✓ 已为ROI '{roiNode.GetName()}' 添加观察器")

    except Exception as e:
        print(f"添加ROI观察器失败: {e}")

def onToggleROIVisibility(widget):
    """切换ROI显示/隐藏状态"""
    try:
        # 获取所有ROI节点
        roiNodes = slicer.util.getNodesByClass("vtkMRMLMarkupsROINode")

        if not roiNodes:
            print("⚠️ 没有找到ROI节点")
            return

        # 检查第一个ROI的当前可见性状态
        firstROI = roiNodes[0]
        displayNode = firstROI.GetDisplayNode()
        if not displayNode:
            return

        current_visibility = displayNode.GetVisibility()
        new_visibility = not current_visibility

        # 切换所有ROI的可见性
        for roiNode in roiNodes:
            displayNode = roiNode.GetDisplayNode()
            if displayNode:
                displayNode.SetVisibility(new_visibility)
                displayNode.SetVisibility2D(new_visibility)

        # 更新按钮文本（不改变颜色）
        if hasattr(widget, 'toggleROIButton'):
            if new_visibility:
                widget.toggleROIButton.setText("隐藏ROI")
                widget.toggleROIButton.setStyleSheet("QPushButton { min-height: 25px; font-size: 12px; }")
                print(f"✓ 已显示 {len(roiNodes)} 个ROI")
            else:
                widget.toggleROIButton.setText("显示ROI")
                widget.toggleROIButton.setStyleSheet("QPushButton { min-height: 25px; font-size: 12px; }")
                print(f"✓ 已隐藏 {len(roiNodes)} 个ROI")

        # 刷新视图
        for viewName in ['Red', 'Yellow', 'Green']:
            sliceWidget = slicer.app.layoutManager().sliceWidget(viewName)
            if sliceWidget:
                sliceWidget.sliceView().forceRender()

    except Exception as e:
        print(f"❌ 切换ROI可见性失败: {e}")

def clearOldROIs():
    """清理旧的ROI节点"""
    try:
        roiNodes = slicer.util.getNodesByClass("vtkMRMLMarkupsROINode")
        if roiNodes:
            for roiNode in roiNodes:
                slicer.mrmlScene.RemoveNode(roiNode)
            print(f"✓ 已清理 {len(roiNodes)} 个旧ROI节点")
    except Exception as e:
        print(f"清理ROI失败: {e}")

def onCropVolumeWithROI(widget):
    """使用ROI裁切当前选择的CT数据 - 改进版本，正确处理坐标变换"""
    try:
        # 获取当前输入数据
        inputVolume = None
        if hasattr(widget, 'inputSelector') and widget.inputSelector.currentNode():
            inputVolume = widget.inputSelector.currentNode()

        if not inputVolume:
            print("⚠️ 请先选择输入的CT数据")
            return

        # 检查是否是头位调整后的数据
        volumeName = inputVolume.GetName()
        isHeadAdjusted = "HeadAdjusted" in volumeName or "FirstQuadrant" in volumeName

        if not isHeadAdjusted:
            print("⚠️ 警告：当前数据似乎未进行头位调整")
            print("   建议工作流程：1. 头位调整 → 2. 创建ROI → 3. ROI裁切")
            print("   继续裁切可能导致坐标不匹配...")

        # 获取ROI节点
        roiNodes = slicer.util.getNodesByClass("vtkMRMLMarkupsROINode")
        if not roiNodes:
            print("⚠️ 没有找到ROI节点，请先创建ROI")
            return

        # 使用最后创建的ROI
        roiNode = roiNodes[-1]

        print(f"开始使用ROI '{roiNode.GetName()}' 裁切数据 '{inputVolume.GetName()}'...")
        print("使用改进的裁切方法，确保坐标一致性...")

        # 创建输出体积节点
        outputVolume = slicer.mrmlScene.AddNewNodeByClass("vtkMRMLScalarVolumeNode")
        outputVolume.SetName(f"{inputVolume.GetName()}_Cropped")

        # 使用Slicer内置的Crop Volume功能
        cropVolumeLogic = slicer.modules.cropvolume.logic()

        # 设置裁切参数
        cropVolumeParameterNode = slicer.vtkMRMLCropVolumeParametersNode()
        cropVolumeParameterNode.SetInputVolumeNodeID(inputVolume.GetID())
        cropVolumeParameterNode.SetROINodeID(roiNode.GetID())
        cropVolumeParameterNode.SetOutputVolumeNodeID(outputVolume.GetID())
        cropVolumeParameterNode.SetVoxelBased(True)  # 基于体素的裁切

        # 执行裁切
        cropVolumeLogic.Apply(cropVolumeParameterNode)

        # 自动选择裁切后的结果
        if hasattr(widget, 'inputSelector'):
            widget.inputSelector.setCurrentNode(outputVolume)

        # 使用安全的视图刷新
        safeRefreshViews(outputVolume)

        print(f"✓ 裁切完成！新数据: '{outputVolume.GetName()}'")
        print("  裁切后的数据已自动选中，视图已刷新")

    except Exception as e:
        print(f"❌ 裁切失败: {e}")
        import traceback
        traceback.print_exc()



def refreshAllViews(volume=None):
    """刷新所有视图以显示新的数据 - 保持头位调整"""
    try:
        # 不重置3D视图，避免影响头位调整
        layoutManager = slicer.app.layoutManager()
        # if layoutManager:
        #     layoutManager.resetThreeDViews()  # 注释掉，避免重置头位调整

        # 刷新切片视图
        if volume:
            # 设置背景体积
            compositeNodes = slicer.util.getNodesByClass('vtkMRMLSliceCompositeNode')
            for compositeNode in compositeNodes:
                compositeNode.SetBackgroundVolumeID(volume.GetID())

        # 强制刷新所有切片视图
        for viewName in ['Red', 'Yellow', 'Green']:
            sliceWidget = slicer.app.layoutManager().sliceWidget(viewName)
            if sliceWidget:
                sliceWidget.sliceLogic().FitSliceToAll()
                sliceWidget.sliceView().forceRender()

        print("✓ 已刷新所有视图")

    except Exception as e:
        print(f"刷新视图失败: {e}")

def safeRefreshViews(volume=None):
    """安全地刷新视图，不影响头位调整等变换"""
    try:
        print("安全刷新视图中...")

        # 只刷新切片视图的内容，不重置方向
        if volume:
            # 设置背景体积
            compositeNodes = slicer.util.getNodesByClass('vtkMRMLSliceCompositeNode')
            for compositeNode in compositeNodes:
                compositeNode.SetBackgroundVolumeID(volume.GetID())

        # 温和地刷新切片视图，不重置方向
        for viewName in ['Red', 'Yellow', 'Green']:
            sliceWidget = slicer.app.layoutManager().sliceWidget(viewName)
            if sliceWidget:
                # 只适配内容到视图，不重置方向
                sliceWidget.sliceLogic().FitSliceToAll()
                sliceWidget.sliceView().forceRender()

        # 对于3D视图，只刷新渲染，不重置相机或变换
        threeDWidget = slicer.app.layoutManager().threeDWidget(0)
        if threeDWidget:
            threeDView = threeDWidget.threeDView()
            if threeDView:
                threeDView.forceRender()  # 只强制渲染，不重置

        print("✓ 安全视图刷新完成")

    except Exception as e:
        print(f"安全视图刷新失败: {e}")

# 简化版本 - 只保留创建功能，删除所有管理、分析和分组功能