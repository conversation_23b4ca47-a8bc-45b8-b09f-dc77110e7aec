import qt
import ctk
import slicer
from slicer.ScriptedLoadableModule import *

# 导入工具函数
try:
    from Resources.HUPseudoColorUtils import (
        HUPseudoColorLogic, 
        HUColorRangeWidget,
        handle_input_changed,
        handle_save_config,
        handle_load_config,
        handle_remove_mapping,
        handle_delete_mapping,
        handle_apply_mapping,
        handle_refresh_hu_info,
        handle_transparency_changed,
        handle_transparency_3d_changed,
        handle_3d_display_changed
    )
    from Resources.SlicePlaneTransformBasic import addBasicSlicePlaneTransformToWidget
    from Resources.HUPreprocessing import HUPreprocessor, onOriginSetting, moveToFirstQuadrant, refreshCTSliceViews
    # 导入ROI管理器函数 - 简化版本，只保留创建功能
    from Resources.ROIManager import onCreateROI, setupROIObserver, onToggleROIVisibility, onCropVolumeWithROI
    ROI_MANAGER_AVAILABLE = True
    print("✓ 成功导入所有工具函数")
except ImportError as e:
    print(f"警告: 无法导入工具函数: {e}")
    # 简化的备用实现
    from Resources.HUPseudoColorUtils import HUPseudoColorLogic, HUColorRangeWidget
    def handle_input_changed(widget): pass
    def handle_save_config(widget): pass
    def handle_load_config(widget): pass
    def handle_remove_mapping(widget): pass
    def handle_delete_mapping(widget, mappingWidget): pass
    def handle_apply_mapping(widget): pass
    def handle_refresh_hu_info(widget): pass
    def handle_transparency_changed(widget): pass
    def handle_transparency_3d_changed(widget): pass
    def handle_3d_display_changed(widget): pass
    def addBasicSlicePlaneTransformToWidget(widget): return None
    HUPreprocessor = None
    def onOriginSetting(widget): pass
    def moveToFirstQuadrant(widget, inputVolume): return False
    def refreshCTSliceViews(widget): return False
    # ROI管理器备用函数 - 简化版本
    def onCreateROI(widget): pass
    def setupROIObserver(widget): pass
    def onToggleROIVisibility(widget): pass
    def onCropVolumeWithROI(widget): pass

#
# HUPseudoColor
#

class HUPseudoColor(ScriptedLoadableModule):
    """HU值伪彩模块"""

    def __init__(self, parent):
        ScriptedLoadableModule.__init__(self, parent)
        self.parent.title = "HU值伪彩"
        self.parent.categories = ["TeethLink--JiuYuan"]
        self.parent.dependencies = []
        self.parent.contributors = ["JiuYuan Development Team"]
        self.parent.helpText = """
HU值伪彩显示功能 - 将CT数据的HU值映射为不同颜色进行可视化
主要功能:
• HU值范围颜色映射
• 实时预览和透明度调节
• 2D切片和3D体积渲染
• 切片平面变换工具
• 配置保存和加载
"""

#
# HUPseudoColorWidget
#

class HUPseudoColorWidget(ScriptedLoadableModuleWidget):
    """HU值伪彩交互界面"""

    def __init__(self, parent=None):
        ScriptedLoadableModuleWidget.__init__(self, parent)
        self.logic = HUPseudoColorLogic()
        self.colorControlPoints = []
        self.previewTimer = qt.QTimer()
        self.previewTimer.timeout.connect(self.updatePreview)
        self.previewTimer.setSingleShot(True)

    def setup(self):
        ScriptedLoadableModuleWidget.setup(self)

        # 设置各个功能区域
        self.setupInputSection()
        self.setupDataPreprocessingSection()
        self.setupSlicePlaneTransformSection()
        self.setupColorMappingSection()

        # 状态显示
        self.statusLabel = qt.QLabel("就绪")
        self.statusLabel.setStyleSheet("color: green; font-style: italic;")
        self.layout.addWidget(self.statusLabel)

        # 连接信号
        self.connectSignals()

        # 初始化默认映射
        self.initializeDefaultMappings()

        # 添加垂直间距
        self.layout.addStretch(1)

        # 检查初始输入
        self.checkInitialInput()

        # 设置ROI观察器，监听现有ROI的变化
        try:
            setupROIObserver(self)
        except Exception as e:
            print(f"设置ROI观察器失败: {e}")

    def setupInputSection(self):
        """设置输入数据区域"""
        inputCollapsibleButton = ctk.ctkCollapsibleButton()
        inputCollapsibleButton.text = "输入数据"
        self.layout.addWidget(inputCollapsibleButton)
        inputFormLayout = qt.QFormLayout(inputCollapsibleButton)

        # 输入体积选择器
        self.inputSelector = slicer.qMRMLNodeComboBox()
        self.inputSelector.nodeTypes = ["vtkMRMLScalarVolumeNode"]
        self.inputSelector.selectNodeUponCreation = True
        self.inputSelector.addEnabled = False
        self.inputSelector.removeEnabled = False
        self.inputSelector.noneEnabled = False
        self.inputSelector.showHidden = False
        self.inputSelector.showChildNodeTypes = False
        self.inputSelector.setMRMLScene(slicer.mrmlScene)
        self.inputSelector.setToolTip("选择要进行HU值伪彩处理的CT数据")
        inputFormLayout.addRow("输入CT数据: ", self.inputSelector)

    def setupDataPreprocessingSection(self):
        """设置数据预处理区域"""
        preprocessingCollapsibleButton = ctk.ctkCollapsibleButton()
        preprocessingCollapsibleButton.text = "数据预处理"
        preprocessingCollapsibleButton.collapsed = False
        self.layout.addWidget(preprocessingCollapsibleButton)
        preprocessingFormLayout = qt.QVBoxLayout(preprocessingCollapsibleButton)

        # HU值映射配置区域 - 使用GroupBox而不是CollapsibleButton
        huMappingGroupBox = qt.QGroupBox("HU值映射配置")
        preprocessingFormLayout.addWidget(huMappingGroupBox)
        huMappingFormLayout = qt.QFormLayout(huMappingGroupBox)

        # 映射方法选择
        self.mappingMethodCombo = qt.QComboBox()
        self.mappingMethodCombo.addItems(["linear", "percentile", "zscore", "adaptive"])
        self.mappingMethodCombo.setToolTip("选择HU值映射方法")
        huMappingFormLayout.addRow("映射方法: ", self.mappingMethodCombo)

        # 目标范围设置
        rangeLayout = qt.QHBoxLayout()
        self.minRangeSpinBox = qt.QDoubleSpinBox()
        self.minRangeSpinBox.setRange(-3000, 3000)
        self.minRangeSpinBox.setValue(-1000)
        self.minRangeSpinBox.setToolTip("目标范围最小值")
        rangeLayout.addWidget(qt.QLabel("最小值:"))
        rangeLayout.addWidget(self.minRangeSpinBox)

        self.maxRangeSpinBox = qt.QDoubleSpinBox()
        self.maxRangeSpinBox.setRange(-3000, 3000)
        self.maxRangeSpinBox.setValue(3000)
        self.maxRangeSpinBox.setToolTip("目标范围最大值")
        rangeLayout.addWidget(qt.QLabel("最大值:"))
        rangeLayout.addWidget(self.maxRangeSpinBox)
        
        huMappingFormLayout.addRow("目标范围: ", rangeLayout)

        # 预处理按钮 - 同一行铺满
        buttonRowLayout = qt.QHBoxLayout()
        
        self.applyHUMappingButton = qt.QPushButton("应用HU值映射")
        self.applyHUMappingButton.setToolTip("将HU值映射应用到CT数据")
        self.applyHUMappingButton.clicked.connect(self.onApplyHUMapping)
        self.applyHUMappingButton.setStyleSheet("QPushButton { min-height: 20px; }")
        buttonRowLayout.addWidget(self.applyHUMappingButton)
        
        self.originSettingButton = qt.QPushButton("原点设置")
        self.originSettingButton.setToolTip("设置数据原点和坐标系")
        self.originSettingButton.clicked.connect(self.onOriginSetting)
        self.originSettingButton.setStyleSheet("QPushButton { min-height: 20px; }")
        buttonRowLayout.addWidget(self.originSettingButton)
        
        huMappingFormLayout.addRow(buttonRowLayout)

    def setupColorMappingSection(self):
        """设置颜色映射区域"""
        colorCollapsibleButton = ctk.ctkCollapsibleButton()
        colorCollapsibleButton.text = "HU颜色映射"
        self.layout.addWidget(colorCollapsibleButton)
        colorFormLayout = qt.QVBoxLayout(colorCollapsibleButton)

        # 配置管理
        configLayout = qt.QHBoxLayout()
        configLayout.addWidget(qt.QLabel("配置管理:"))

        self.saveConfigButton = qt.QPushButton("保存配置")
        self.saveConfigButton.setMaximumWidth(80)
        self.saveConfigButton.setStyleSheet("QPushButton { min-height: 20px; }")
        configLayout.addWidget(self.saveConfigButton)

        self.loadConfigButton = qt.QPushButton("加载配置")
        self.loadConfigButton.setMaximumWidth(80)
        self.loadConfigButton.setStyleSheet("QPushButton { min-height: 20px; }")
        configLayout.addWidget(self.loadConfigButton)

        configLayout.addStretch(1)
        colorFormLayout.addLayout(configLayout)

        # 颜色映射控制点
        self.colorMappingGroupBox = qt.QGroupBox("颜色映射")
        colorFormLayout.addWidget(self.colorMappingGroupBox)
        self.colorMappingLayout = qt.QVBoxLayout(self.colorMappingGroupBox)

        # 添加/删除按钮
        buttonLayout = qt.QHBoxLayout()
        self.addMappingButton = qt.QPushButton("添加映射")
        self.addMappingButton.setMaximumWidth(80)
        self.addMappingButton.setStyleSheet("QPushButton { min-height: 20px; }")
        buttonLayout.addWidget(self.addMappingButton)

        self.removeMappingButton = qt.QPushButton("删除映射")
        self.removeMappingButton.setMaximumWidth(80)
        self.removeMappingButton.setStyleSheet("QPushButton { min-height: 20px; }")
        buttonLayout.addWidget(self.removeMappingButton)

        buttonLayout.addStretch(1)
        self.colorMappingLayout.addLayout(buttonLayout)

        # 实时预览、平滑过渡和3D体积渲染选项
        optionsLayout = qt.QHBoxLayout()
        
        self.realTimePreview = qt.QCheckBox("实时预览")
        self.realTimePreview.checked = True
        optionsLayout.addWidget(self.realTimePreview)

        self.smoothTransition = qt.QCheckBox("平滑过渡")
        self.smoothTransition.checked = True
        optionsLayout.addWidget(self.smoothTransition)

        self.enable3DDisplay = qt.QCheckBox("启用3D体积渲染")
        self.enable3DDisplay.checked = True
        optionsLayout.addWidget(self.enable3DDisplay)

        optionsLayout.addStretch(1)
        colorFormLayout.addLayout(optionsLayout)

        # 透明度控制
        transparencyGroupBox = qt.QGroupBox("透明度控制")
        colorFormLayout.addWidget(transparencyGroupBox)
        transparencyLayout = qt.QVBoxLayout(transparencyGroupBox)

        # 2D切片透明度
        slice2DLayout = qt.QHBoxLayout()
        slice2DLayout.addWidget(qt.QLabel("2D切片透明度:"))
        
        self.transparencySlider = qt.QSlider(qt.Qt.Horizontal)
        self.transparencySlider.setRange(0, 100)
        self.transparencySlider.setValue(50)
        self.transparencySlider.setMinimumWidth(600)
        self.transparencySlider.setMaximumWidth(600)
        slice2DLayout.addWidget(self.transparencySlider)
        
        self.transparencyLabel = qt.QLabel("50%")
        self.transparencyLabel.setMinimumWidth(40)
        slice2DLayout.addWidget(self.transparencyLabel)
        
        slice2DLayout.addStretch(1)
        transparencyLayout.addLayout(slice2DLayout)

        # 3D体积透明度
        volume3DLayout = qt.QHBoxLayout()
        volume3DLayout.addWidget(qt.QLabel("3D体积透明度:"))
        
        self.transparency3DSlider = qt.QSlider(qt.Qt.Horizontal)
        self.transparency3DSlider.setRange(0, 100)
        self.transparency3DSlider.setValue(30)
        self.transparency3DSlider.setMinimumWidth(600)
        self.transparency3DSlider.setMaximumWidth(600)
        volume3DLayout.addWidget(self.transparency3DSlider)
        
        self.transparency3DLabel = qt.QLabel("30%")
        self.transparency3DLabel.setMinimumWidth(40)
        volume3DLayout.addWidget(self.transparency3DLabel)
        
        volume3DLayout.addStretch(1)
        transparencyLayout.addLayout(volume3DLayout)

        # 控制按钮
        controlGroupBox = qt.QGroupBox("控制")
        colorFormLayout.addWidget(controlGroupBox)
        controlLayout = qt.QVBoxLayout(controlGroupBox)

        # 应用按钮
        self.applyButton = qt.QPushButton("应用颜色映射")
        self.applyButton.toolTip = "将HU值颜色映射应用到CT数据"
        self.applyButton.enabled = False
        self.applyButton.setStyleSheet("QPushButton { background-color: #4CAF50; color: white; font-weight: bold; min-height: 30px; }")
        controlLayout.addWidget(self.applyButton)



    def setupSlicePlaneTransformSection(self):
        """设置切片平面变换工具区域"""
        try:
            print("开始设置切片平面变换工具...")
            self.slicePlaneTransformWidget = addBasicSlicePlaneTransformToWidget(self)
            if self.slicePlaneTransformWidget:
                print("✓ 切片平面变换工具已成功添加")
            else:
                print("✗ 切片平面变换工具添加失败")
                self.createFallbackTransformSection()
        except Exception as e:
            print(f"✗ 设置切片平面变换工具失败: {e}")
            self.createFallbackTransformSection()



        # 简化版本 - 删除所有管理功能

        # 简化版本完成 - 只保留ROI创建功能

    def createFallbackTransformSection(self):
        """创建备用的变换工具提示"""
        try:
            fallbackCollapsibleButton = ctk.ctkCollapsibleButton()
            fallbackCollapsibleButton.text = "切片变换工具 (不可用)"
            fallbackCollapsibleButton.collapsed = True
            self.layout.addWidget(fallbackCollapsibleButton)
            
            fallbackLayout = qt.QVBoxLayout(fallbackCollapsibleButton)
            infoLabel = qt.QLabel("切片变换工具加载失败，请检查相关文件是否存在。")
            infoLabel.setStyleSheet("color: orange; font-style: italic;")
            fallbackLayout.addWidget(infoLabel)
        except Exception as e:
            print(f"创建备用变换工具提示失败: {e}")

    def connectSignals(self):
        """连接信号"""
        self.inputSelector.connect("currentNodeChanged(vtkMRMLNode*)", self.onInputChanged)
        self.saveConfigButton.connect('clicked(bool)', self.onSaveConfig)
        self.loadConfigButton.connect('clicked(bool)', self.onLoadConfig)
        self.addMappingButton.connect('clicked(bool)', self.onAddMapping)
        self.removeMappingButton.connect('clicked(bool)', self.onRemoveMapping)
        self.applyButton.connect('clicked(bool)', self.onApplyMapping)
        self.transparencySlider.connect('valueChanged(int)', self.onTransparencyChanged)
        self.transparency3DSlider.connect('valueChanged(int)', self.onTransparency3DChanged)
        self.enable3DDisplay.connect('toggled(bool)', self.on3DDisplayChanged)

    def initializeDefaultMappings(self):
        """初始化默认HU颜色映射 - 使用模板配置"""
        default_ranges = [
            {"min_hu": -2000, "max_hu": 0, "color": "#000000", "name": "1"},
            {"min_hu": 0, "max_hu": 150, "color": "#4e0000", "name": "2"},
            {"min_hu": 150, "max_hu": 250, "color": "#960000", "name": "3"},
            {"min_hu": 250, "max_hu": 350, "color": "#ff0000", "name": "4"},
            {"min_hu": 350, "max_hu": 500, "color": "#ffa500", "name": "5"},
            {"min_hu": 500, "max_hu": 600, "color": "#ffff00", "name": "6"},
            {"min_hu": 600, "max_hu": 850, "color": "#9bff00", "name": "7"},
            {"min_hu": 850, "max_hu": 1000, "color": "#00ff00", "name": "8"},
            {"min_hu": 1000, "max_hu": 1250, "color": "#009b00", "name": "9"},
            {"min_hu": 1250, "max_hu": 2000, "color": "#006400", "name": "10"},
            {"min_hu": 2000, "max_hu": 3000, "color": "#004221", "name": "13"}
        ]

        for range_data in default_ranges:
            self.addColorMapping(range_data["min_hu"], range_data["max_hu"], 
                               range_data["color"], range_data["name"])

    def addColorMapping(self, min_hu=-100, max_hu=100, color="#FF0000", name="组织"):
        """添加颜色映射"""
        mappingWidget = HUColorRangeWidget(min_hu, max_hu, color, name)
        mappingWidget.valueChanged.connect(self.onColorMappingChanged)
        mappingWidget.deleteRequested.connect(self.onDeleteMapping)
        self.colorControlPoints.append(mappingWidget)
        self.colorMappingLayout.addWidget(mappingWidget)
        return mappingWidget

    def getColorMapping(self):
        """获取当前颜色映射配置"""
        mappings = []
        for widget in self.colorControlPoints:
            mappings.append({
                "type": "range",
                "min_hu": widget.getMinHU(),
                "max_hu": widget.getMaxHU(),
                "color": widget.getColor(),
                "name": widget.getName()
            })
        return mappings

    def checkInitialInput(self):
        """检查初始输入状态"""
        if self.inputSelector.currentNode():
            self.onInputChanged()

    # 事件处理函数 - 调用工具函数
    def onInputChanged(self):
        handle_input_changed(self)

    def onSaveConfig(self):
        handle_save_config(self)

    def onLoadConfig(self):
        handle_load_config(self)

    def onAddMapping(self):
        self.addColorMapping()
        self.schedulePreviewUpdate()

    def onRemoveMapping(self):
        handle_remove_mapping(self)

    def onDeleteMapping(self, mappingWidget):
        handle_delete_mapping(self, mappingWidget)

    def onColorMappingChanged(self):
        if self.realTimePreview.checked:
            self.schedulePreviewUpdate()

    def onApplyMapping(self):
        handle_apply_mapping(self)

    def onRefreshDataInfo(self):
        handle_refresh_hu_info(self)

    def onTransparencyChanged(self):
        handle_transparency_changed(self)

    def onTransparency3DChanged(self):
        handle_transparency_3d_changed(self)

    def on3DDisplayChanged(self):
        handle_3d_display_changed(self)

    def onHUMapping(self):
        """HU值映射按钮处理"""
        slicer.util.infoDisplay("HU值映射功能：\n\n请在下方的'HU颜色映射'区域中配置不同HU值范围对应的颜色。\n\n• 可以添加、删除映射范围\n• 可以调整HU值范围和对应颜色\n• 支持保存和加载配置\n• 支持实时预览")

    def onLayerMapping(self):
        """层高映射按钮处理"""
        inputVolume = self.inputSelector.currentNode()
        if inputVolume:
            spacing = inputVolume.GetSpacing()
            dimensions = inputVolume.GetImageData().GetDimensions()
            info = f"当前CT数据层高信息：\n\n"
            info += f"• 像素间距: {spacing[0]:.2f} × {spacing[1]:.2f} × {spacing[2]:.2f} mm\n"
            info += f"• 数据维度: {dimensions[0]} × {dimensions[1]} × {dimensions[2]}\n"
            info += f"• 层厚: {spacing[2]:.2f} mm\n"
            info += f"• 总层数: {dimensions[2]} 层"
            slicer.util.infoDisplay(info)
        else:
            slicer.util.warningDisplay("请先选择输入的CT数据")

    def onOriginSetting(self):
        """原点设置按钮处理 - 调用HUPreprocessing模块中的方法"""
        try:
            # 调用导入的原点设置函数
            onOriginSetting(self)
        except Exception as e:
            print(f"调用原点设置功能失败: {e}")
            slicer.util.errorDisplay(f"原点设置功能不可用: {e}")

    def moveToFirstQuadrant(self, inputVolume):
        """将CT数据移动到第一象限 - 调用HUPreprocessing模块中的方法"""
        try:
            # 调用导入的移动到第一象限函数
            return moveToFirstQuadrant(self, inputVolume)
        except Exception as e:
            print(f"调用移动到第一象限功能失败: {e}")
            return False

    def refreshCTSliceViews(self):
        """刷新CT切片视图 - 调用HUPreprocessing模块中的方法"""
        try:
            # 调用导入的刷新切片视图函数
            return refreshCTSliceViews(self)
        except Exception as e:
            print(f"调用刷新切片视图功能失败: {e}")
            return False

    def onApplyHUMapping(self):
        """应用HU值映射处理"""
        inputVolume = self.inputSelector.currentNode()
        if not inputVolume:
            slicer.util.warningDisplay("请先选择输入的CT数据")
            return

        if not HUPreprocessor:
            slicer.util.errorDisplay("HU预处理模块未加载，无法执行映射")
            return

        try:
            self.statusLabel.setText("正在进行HU值映射...")
            self.statusLabel.setStyleSheet("color: blue; font-weight: bold;")

            # 获取映射参数
            mapping_params = {
                'method': self.mappingMethodCombo.currentText,
                'target_range': (self.minRangeSpinBox.value, self.maxRangeSpinBox.value)
            }

            # 执行HU值映射
            result = self.performHUMapping(inputVolume, mapping_params)

            if result:
                self.statusLabel.setText("HU值映射完成")
                self.statusLabel.setStyleSheet("color: green; font-weight: bold;")
                slicer.util.infoDisplay("HU值映射完成！\n\n映射后的数据已创建，可以继续进行颜色映射。")
            else:
                self.statusLabel.setText("HU值映射失败")
                self.statusLabel.setStyleSheet("color: red; font-weight: bold;")

        except Exception as e:
            slicer.util.errorDisplay(f"HU值映射失败: {e}")
            self.statusLabel.setText("处理失败")
            self.statusLabel.setStyleSheet("color: red; font-weight: bold;")

    def performHUMapping(self, inputVolume, mapping_params):
        """执行HU值映射"""
        try:
            import numpy as np
            
            # 获取体积数据
            volumeArray = slicer.util.arrayFromVolume(inputVolume)
            
            # 创建HU预处理器
            preprocessor = HUPreprocessor()
            method = mapping_params.get('method', 'linear')
            target_range = mapping_params.get('target_range', (-1000, 3000))
            
            print(f"应用HU值映射: 方法={method}, 目标范围={target_range}")
            
            # 根据方法应用映射
            if method == 'window_level':
                window_width = mapping_params.get('window_width', 400)
                window_level = mapping_params.get('window_level', 40)
                mapped_array = preprocessor.window_level_mapping(
                    volumeArray, window_width, window_level, target_range
                )
            elif method == 'adaptive':
                mapped_array = preprocessor.adaptive_mapping(
                    volumeArray, target_range
                )
            else:
                mapped_array = preprocessor.custom_range_mapping(
                    volumeArray, target_range, method=method
                )
            
            # 创建输出体积
            outputVolume = slicer.mrmlScene.AddNewNodeByClass("vtkMRMLScalarVolumeNode")
            method_suffix = f"_{method}"
            outputVolume.SetName(f"{inputVolume.GetName()}_HU_Mapped{method_suffix}")
            
            # 复制几何信息并更新数据
            outputVolume.CopyOrientation(inputVolume)
            slicer.util.updateVolumeFromArray(outputVolume, mapped_array)
            
            # 自动选择映射后的数据作为新的输入
            self.inputSelector.setCurrentNode(outputVolume)
            
            print("HU值映射完成")
            return True
            
        except Exception as e:
            print(f"HU值映射失败: {e}")
            return False

    def schedulePreviewUpdate(self):
        """安排预览更新"""
        self.previewTimer.start(500)  # 500ms延迟

    def updatePreview(self):
        """更新预览"""
        try:
            if self.realTimePreview.checked and self.inputSelector.currentNode():
                inputVolume = self.inputSelector.currentNode()
                colorMapping = self.getColorMapping()
                # 这里可以添加预览更新逻辑
                pass
        except Exception as e:
            print(f"预览更新失败: {e}")


#
# HUPseudoColorTest
#

class HUPseudoColorTest(ScriptedLoadableModuleTest):
    """测试类"""

    def setUp(self):
        slicer.mrmlScene.Clear()

    def runTest(self):
        self.setUp()
        self.test_HUPseudoColor1()

    def test_HUPseudoColor1(self):
        self.delayDisplay("开始测试")
        logic = HUPseudoColorLogic()
        self.delayDisplay('测试通过')