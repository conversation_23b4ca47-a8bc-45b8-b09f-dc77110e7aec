# HU伪彩色模块 - 切片平面变换工具

## 项目概述

这个项目为3D Slicer的HU伪彩色模块添加了切片平面变换功能，允许用户通过调节切片平面的交线来实现CT数据的旋转和移动变换。

## 文件结构

```
├── HUPseudoColor.py                    # 主模块文件
├── Resources/
│   ├── HUPseudoColorUtils.py          # HU伪彩色工具函数
│   ├── SlicePlaneTransformBasic.py    # 切片平面变换工具 - 基础版本（推荐）
│   ├── SlicePlaneTransformSync.py     # 切片平面变换工具 - 双向同步版本
│   ├── SlicePlaneTransformUtils.py    # 重定向文件（向后兼容）
│   └── Icons/                         # 图标资源
├── test_slice_transform.py            # 测试脚本
├── 切片变换工具使用指南.md             # 详细使用说明
└── README.md                          # 项目说明
```

## 核心功能

### 1. 切片平面变换控制
- **实时切片平面调节**: 支持轴状面、矢状面、冠状面的独立控制
- **6自由度变换**: 3轴旋转 + 3轴平移
- **交线可视化**: 实时显示切片平面之间的交线

### 2. CT数据变换应用
- **坐标系转换**: 将切片平面变换转换为CT数据变换
- **数据重采样**: 生成变换后的新CT数据
- **无损操作**: 保持原始数据不变

### 3. 与HU伪彩色集成
- **统一界面**: 集成在HU伪彩色模块中
- **工作流优化**: 先变换再应用伪彩色
- **实时预览**: 支持变换过程的实时反馈

## 技术实现

### 坐标系转换原理

在3D Slicer中，切片平面的调节实际上是在操作切片坐标系到RAS（Right-Anterior-Superior）坐标系的变换矩阵。这个工具的核心是将这种变换转化为对CT数据本身的变换。

```python
# 变换链
切片坐标系 → RAS坐标系 → CT数据坐标系
```

### 关键算法

1. **切片变换获取**:
```python
sliceNode = slicer.app.layoutManager().sliceWidget(sliceName).mrmlSliceNode()
sliceToRAS = sliceNode.GetSliceToRAS()
```

2. **变换矩阵构建**:
```python
transform = vtk.vtkTransform()
transform.PostMultiply()
transform.Translate(transX, transY, transZ)
transform.RotateX(rotX)
transform.RotateY(rotY) 
transform.RotateZ(rotZ)
```

3. **CT数据重采样**:
```python
parameters = {
    'inputVolume': inputVolume.GetID(),
    'outputVolume': outputVolume.GetID(),
    'transformationFile': transformNode.GetID(),
    'interpolationType': 'linear'
}
slicer.cli.runSync(resampleModule, None, parameters)
```

## 使用场景

### 医学影像分析
- **标准化方向**: 将CT数据旋转到标准解剖位置
- **多时相配准**: 调整不同时间点CT数据的方向一致性
- **病灶观察**: 旋转到最佳观察角度

### 手术规划
- **体位匹配**: 调整CT数据方向匹配手术体位
- **路径规划**: 在最佳角度下进行手术路径设计
- **导航准备**: 为手术导航系统准备标准化数据

### 教学演示
- **解剖展示**: 从多角度展示解剖结构
- **病例讨论**: 调整到最佳展示角度
- **对比分析**: 统一不同病例的显示方向

## 安装和使用

### 1. 安装到3D Slicer

1. 将整个项目文件夹复制到Slicer的模块目录
2. 重启3D Slicer
3. 在模块列表中找到"HU值伪彩"模块

### 2. 基本使用流程

1. **加载数据**: 选择CT数据作为输入
2. **调节变换**: 使用切片平面变换工具调节方向
3. **应用变换**: 点击"应用到CT数据"生成变换后的数据
4. **应用伪彩**: 对变换后的数据应用HU伪彩色映射

### 3. 高级功能

- **多切片控制**: 同时控制多个切片平面
- **精确调节**: 使用滑块进行精确的角度和位置调节
- **实时预览**: 观察变换效果的实时反馈

## 技术特点

### 优势
- **直观操作**: 通过切片交线直观地控制变换
- **实时反馈**: 变换效果立即可见
- **无损处理**: 原始数据保持完整
- **集成设计**: 与HU伪彩色功能无缝集成

### 创新点
- **切片交线控制**: 利用Slicer的切片交线功能进行变换控制
- **坐标系转换**: 实现了切片坐标系到CT数据坐标系的准确转换
- **实时可视化**: 变换过程的实时可视化反馈

## 开发说明

### 代码结构
- `SlicePlaneTransformWidget`: 用户界面控件
- `SlicePlaneTransformLogic`: 核心算法逻辑
- `addSlicePlaneTransformToWidget`: 集成函数

### 扩展开发
工具采用模块化设计，便于扩展：
- 添加新的变换类型
- 集成自动配准算法
- 支持批量处理功能

### 调试和测试
- 使用`test_slice_transform.py`进行基本测试
- 在3D Slicer控制台查看详细日志
- 通过切片视图验证变换效果

## 注意事项

1. **内存使用**: 变换操作会创建新的数据副本
2. **处理时间**: 大数据集可能需要较长处理时间
3. **精度限制**: 受滑块分辨率和插值算法影响
4. **坐标系理解**: 需要理解不同坐标系的定义和转换

## 未来改进

- [ ] 支持数值输入变换参数
- [ ] 添加预设的标准解剖位置
- [ ] 集成自动配准功能
- [ ] 支持批量处理多个数据集
- [ ] 优化大数据集的处理性能

## 技术支持

如有问题或建议，请通过以下方式联系：
- 查看详细使用说明：`切片平面变换工具使用说明.md`
- 运行测试脚本：`python test_slice_transform.py`
- 查看3D Slicer控制台的调试信息

---

这个工具为3D Slicer的HU伪彩色功能增加了强大的数据预处理能力，使用户能够在最佳观察角度下进行医学影像分析和可视化。