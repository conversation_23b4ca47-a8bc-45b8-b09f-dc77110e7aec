import logging
import os

import slicer
from slicer.ScriptedLoadableModule import *

import qt
import ctk
import numpy as np

# 导入HU预处理模块
try:
    from Resources.HUPreprocessing import HUPreprocessor
    print("✓ 成功导入HU预处理模块")
except ImportError as e:
    print(f"警告: 无法导入HU预处理模块: {e}")
    HUPreprocessor = None

#
# HUMapping
#

class HUMapping(ScriptedLoadableModule):
    """HU值映射模块"""

    def __init__(self, parent):
        ScriptedLoadableModule.__init__(self, parent)
        self.parent.title = "HU值映射"
        self.parent.categories = ["TeethLink--JiuYuan"]
        self.parent.dependencies = []
        self.parent.contributors = ["JiuYuan Development Team"]
        self.parent.helpText = """
HU值映射分析功能
"""

#
# HUMappingWidget
#

class HUMappingWidget(ScriptedLoadableModuleWidget):
    """HU值映射界面"""

    def __init__(self, parent=None):
        ScriptedLoadableModuleWidget.__init__(self, parent)
        self.logic = None

    def setup(self):
        ScriptedLoadableModuleWidget.setup(self)
        self.logic = HUMappingLogic()

        # Title
        titleLabel = qt.QLabel("HU值映射")
        titleLabel.setStyleSheet("font-size: 18px; font-weight: bold; color: #45B7D1; margin: 10px;")
        self.layout.addWidget(titleLabel)

        # Input section
        inputCollapsibleButton = ctk.ctkCollapsibleButton()
        inputCollapsibleButton.text = "输入数据"
        self.layout.addWidget(inputCollapsibleButton)
        inputFormLayout = qt.QFormLayout(inputCollapsibleButton)

        # Input volume selector
        self.inputSelector = slicer.qMRMLNodeComboBox()
        self.inputSelector.nodeTypes = ["vtkMRMLScalarVolumeNode"]
        self.inputSelector.selectNodeUponCreation = True
        self.inputSelector.addEnabled = False
        self.inputSelector.removeEnabled = False
        self.inputSelector.noneEnabled = False
        self.inputSelector.showHidden = False
        self.inputSelector.showChildNodeTypes = False
        self.inputSelector.setMRMLScene(slicer.mrmlScene)
        self.inputSelector.setToolTip("选择输入的CT数据")
        inputFormLayout.addRow("输入数据: ", self.inputSelector)

        # Output volume selector
        self.outputSelector = slicer.qMRMLNodeComboBox()
        self.outputSelector.nodeTypes = ["vtkMRMLScalarVolumeNode"]
        self.outputSelector.selectNodeUponCreation = True
        self.outputSelector.addEnabled = True
        self.outputSelector.removeEnabled = True
        self.outputSelector.noneEnabled = True
        self.outputSelector.showHidden = False
        self.outputSelector.showChildNodeTypes = False
        self.outputSelector.setMRMLScene(slicer.mrmlScene)
        self.outputSelector.setToolTip("选择输出数据")
        inputFormLayout.addRow("输出数据: ", self.outputSelector)

        # HU映射配置区域
        mappingCollapsibleButton = ctk.ctkCollapsibleButton()
        mappingCollapsibleButton.text = "HU值映射配置"
        self.layout.addWidget(mappingCollapsibleButton)
        mappingFormLayout = qt.QFormLayout(mappingCollapsibleButton)

        # 映射方法选择
        self.mappingMethodCombo = qt.QComboBox()
        self.mappingMethodCombo.addItems(["linear", "percentile", "zscore", "window_level", "adaptive"])
        self.mappingMethodCombo.setToolTip("选择HU值映射方法")
        mappingFormLayout.addRow("映射方法: ", self.mappingMethodCombo)

        # 目标范围设置
        rangeLayout = qt.QHBoxLayout()
        self.minRangeSpinBox = qt.QDoubleSpinBox()
        self.minRangeSpinBox.setRange(-3000, 3000)
        self.minRangeSpinBox.setValue(-1000)
        self.minRangeSpinBox.setToolTip("目标范围最小值")
        rangeLayout.addWidget(qt.QLabel("最小值:"))
        rangeLayout.addWidget(self.minRangeSpinBox)

        self.maxRangeSpinBox = qt.QDoubleSpinBox()
        self.maxRangeSpinBox.setRange(-3000, 3000)
        self.maxRangeSpinBox.setValue(3000)
        self.maxRangeSpinBox.setToolTip("目标范围最大值")
        rangeLayout.addWidget(qt.QLabel("最大值:"))
        rangeLayout.addWidget(self.maxRangeSpinBox)
        
        mappingFormLayout.addRow("目标范围: ", rangeLayout)

        # 窗宽窗位设置（仅在window_level方法时显示）
        windowLayout = qt.QHBoxLayout()
        self.windowWidthSpinBox = qt.QDoubleSpinBox()
        self.windowWidthSpinBox.setRange(1, 4000)
        self.windowWidthSpinBox.setValue(400)
        self.windowWidthSpinBox.setToolTip("窗宽值")
        windowLayout.addWidget(qt.QLabel("窗宽:"))
        windowLayout.addWidget(self.windowWidthSpinBox)

        self.windowLevelSpinBox = qt.QDoubleSpinBox()
        self.windowLevelSpinBox.setRange(-1000, 3000)
        self.windowLevelSpinBox.setValue(40)
        self.windowLevelSpinBox.setToolTip("窗位值")
        windowLayout.addWidget(qt.QLabel("窗位:"))
        windowLayout.addWidget(self.windowLevelSpinBox)

        self.windowWidget = qt.QWidget()
        self.windowWidget.setLayout(windowLayout)
        mappingFormLayout.addRow("窗宽窗位: ", self.windowWidget)
        self.windowWidget.setVisible(False)  # 默认隐藏

        # 连接映射方法变化信号
        self.mappingMethodCombo.connect('currentTextChanged(QString)', self.onMappingMethodChanged)

        # Process button
        self.processButton = qt.QPushButton("开始HU值映射")
        self.processButton.toolTip = "开始HU值映射分析"
        self.processButton.enabled = False
        self.layout.addWidget(self.processButton)

        # Status
        self.statusLabel = qt.QLabel("就绪 - 请选择输入数据")
        self.statusLabel.setStyleSheet("color: green; font-weight: bold; margin: 10px;")
        self.layout.addWidget(self.statusLabel)

        # Results section
        resultsCollapsibleButton = ctk.ctkCollapsibleButton()
        resultsCollapsibleButton.text = "HU值统计"
        resultsCollapsibleButton.collapsed = True
        self.layout.addWidget(resultsCollapsibleButton)
        resultsLayout = qt.QVBoxLayout(resultsCollapsibleButton)

        self.resultsTextEdit = qt.QTextEdit()
        self.resultsTextEdit.setMaximumHeight(150)
        self.resultsTextEdit.setReadOnly(True)
        resultsLayout.addWidget(self.resultsTextEdit)

        # Connections
        self.processButton.connect('clicked(bool)', self.onProcessButton)
        self.inputSelector.connect("currentNodeChanged(vtkMRMLNode*)", self.onInputChanged)

        # Add vertical spacer
        self.layout.addStretch(1)

    def onMappingMethodChanged(self, method):
        """映射方法改变时的处理"""
        # 只有在window_level方法时显示窗宽窗位设置
        self.windowWidget.setVisible(method == "window_level")

    def onInputChanged(self):
        inputVolume = self.inputSelector.currentNode()
        self.processButton.enabled = inputVolume is not None
        if inputVolume:
            self.statusLabel.setText(f"已选择输入: {inputVolume.GetName()}")
            self.statusLabel.setStyleSheet("color: blue; font-weight: bold;")
        else:
            self.statusLabel.setText("请选择输入数据")
            self.statusLabel.setStyleSheet("color: orange; font-weight: bold;")

    def onProcessButton(self):
        inputVolume = self.inputSelector.currentNode()
        outputVolume = self.outputSelector.currentNode()
        
        if inputVolume:
            try:
                self.statusLabel.setText("正在进行HU值映射...")
                self.statusLabel.setStyleSheet("color: blue; font-weight: bold;")
                
                # 获取映射参数
                mapping_params = {
                    'method': self.mappingMethodCombo.currentText,
                    'target_range': (self.minRangeSpinBox.value, self.maxRangeSpinBox.value),
                    'window_width': self.windowWidthSpinBox.value,
                    'window_level': self.windowLevelSpinBox.value
                }
                
                result, stats = self.logic.performHUMapping(inputVolume, outputVolume, mapping_params)
                
                if result:
                    self.statusLabel.setText("HU值映射完成")
                    self.statusLabel.setStyleSheet("color: green; font-weight: bold;")
                    
                    # Display statistics
                    self.resultsTextEdit.setText(stats)
                    
                    slicer.util.infoDisplay("HU值映射完成")
                else:
                    self.statusLabel.setText("HU值映射失败")
                    self.statusLabel.setStyleSheet("color: red; font-weight: bold;")
                    
            except Exception as e:
                slicer.util.errorDisplay(f"HU值映射失败: {e}")
                self.statusLabel.setText("处理失败")
                self.statusLabel.setStyleSheet("color: red; font-weight: bold;")

    def cleanup(self):
        pass

#
# HUMappingLogic
#

class HUMappingLogic(ScriptedLoadableModuleLogic):
    """HU值映射逻辑"""

    def __init__(self):
        ScriptedLoadableModuleLogic.__init__(self)

    def performHUMapping(self, inputVolume, outputVolume=None, mapping_params=None):
        """执行HU值映射"""
        logging.info("开始HU值映射...")
        
        try:
            # Get volume data
            volumeArray = slicer.util.arrayFromVolume(inputVolume)
            
            # Calculate original HU statistics
            original_min = float(volumeArray.min())
            original_max = float(volumeArray.max())
            original_mean = float(volumeArray.mean())
            original_std = float(volumeArray.std())
            
            # Apply HU preprocessing if HUPreprocessor is available and mapping_params provided
            mapped_array = volumeArray.copy()
            if HUPreprocessor and mapping_params:
                preprocessor = HUPreprocessor()
                method = mapping_params.get('method', 'linear')
                target_range = mapping_params.get('target_range', (-1000, 3000))
                
                print(f"应用HU值映射: 方法={method}, 目标范围={target_range}")
                
                if method == 'window_level':
                    window_width = mapping_params.get('window_width', 400)
                    window_level = mapping_params.get('window_level', 40)
                    mapped_array = preprocessor.window_level_mapping(
                        volumeArray, window_width, window_level, target_range
                    )
                elif method == 'adaptive':
                    mapped_array = preprocessor.adaptive_mapping(
                        volumeArray, target_range
                    )
                else:
                    mapped_array = preprocessor.custom_range_mapping(
                        volumeArray, target_range, method=method
                    )
            
            # Calculate mapped HU statistics
            mapped_min = float(mapped_array.min())
            mapped_max = float(mapped_array.max())
            mapped_mean = float(mapped_array.mean())
            mapped_std = float(mapped_array.std())
            
            # Create statistics text
            stats_text = f"""HU值映射结果:

原始数据统计:
最小值: {original_min:.1f} HU
最大值: {original_max:.1f} HU
平均值: {original_mean:.1f} HU
标准差: {original_std:.1f} HU

映射后统计:
最小值: {mapped_min:.1f} HU
最大值: {mapped_max:.1f} HU
平均值: {mapped_mean:.1f} HU
标准差: {mapped_std:.1f} HU

映射参数:
方法: {mapping_params.get('method', '无') if mapping_params else '无'}
目标范围: {mapping_params.get('target_range', '无') if mapping_params else '无'}

组织分类 (基于HU值):
空气: < -500 HU
脂肪: -500 到 -100 HU
水: -100 到 50 HU
软组织: 50 到 150 HU
骨骼: > 150 HU"""
            
            # Create output volume if specified
            if outputVolume is None:
                outputVolume = slicer.mrmlScene.AddNewNodeByClass("vtkMRMLScalarVolumeNode")
                method_suffix = f"_{mapping_params['method']}" if mapping_params else ""
                outputVolume.SetName(f"{inputVolume.GetName()}_HU_Mapped{method_suffix}")
            
            # Copy geometry and update with mapped data
            outputVolume.CopyOrientation(inputVolume)
            slicer.util.updateVolumeFromArray(outputVolume, mapped_array)
            
            logging.info("HU值映射完成")
            return True, stats_text
            
        except Exception as e:
            logging.error(f"HU值映射失败: {e}")
            return False, f"映射失败: {str(e)}"

#
# HUMappingTest
#

class HUMappingTest(ScriptedLoadableModuleTest):
    def setUp(self):
        slicer.mrmlScene.Clear()

    def runTest(self):
        self.setUp()
        self.test_HUMapping1()

    def test_HUMapping1(self):
        self.delayDisplay("开始测试")
        logic = HUMappingLogic()
        self.delayDisplay("测试完成")