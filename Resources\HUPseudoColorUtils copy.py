"""
HU伪彩工具函数模块
包含所有具体的执行逻辑和工具函数
"""

import logging
import os
import vtk
import qt
import ctk
import slicer
from slicer.ScriptedLoadableModule import *
import numpy as np
import json

#
# HUColorRangeWidget - 颜色范围控件
#

class HUColorRangeWidget(qt.QWidget):
    """单个HU颜色范围映射的控件"""

    valueChanged = qt.Signal()
    deleteRequested = qt.Signal(object)

    def __init__(self, min_hu=-100, max_hu=100, color="#FF0000", name="组织"):
        super().__init__()
        self.setupUI()
        self.setMinHU(min_hu)
        self.setMaxHU(max_hu)
        self.setColor(color)
        self.setName(name)

    def setupUI(self):
        layout = qt.QHBoxLayout(self)
        layout.setContentsMargins(5, 2, 5, 2)

        # Name
        self.nameEdit = qt.QLineEdit()
        self.nameEdit.setMaximumWidth(80)
        self.nameEdit.textChanged.connect(self.valueChanged.emit)
        layout.addWidget(self.nameEdit)

        # Min HU value
        self.minHUSpinBox = qt.QSpinBox()
        self.minHUSpinBox.setRange(-2000, 5000)
        self.minHUSpinBox.setSuffix(" HU")
        self.minHUSpinBox.setMaximumWidth(100)
        self.minHUSpinBox.valueChanged.connect(self.onMinHUChanged)
        layout.addWidget(self.minHUSpinBox)

        # Range separator
        layout.addWidget(qt.QLabel("~"))

        # Max HU value
        self.maxHUSpinBox = qt.QSpinBox()
        self.maxHUSpinBox.setRange(-2000, 5000)
        self.maxHUSpinBox.setSuffix(" HU")
        self.maxHUSpinBox.setMaximumWidth(100)
        self.maxHUSpinBox.valueChanged.connect(self.onMaxHUChanged)
        layout.addWidget(self.maxHUSpinBox)

        # Color button
        self.colorButton = qt.QPushButton()
        self.colorButton.setMaximumWidth(50)
        self.colorButton.setMaximumHeight(25)
        self.colorButton.clicked.connect(self.chooseColor)
        layout.addWidget(self.colorButton)

        # Delete button
        self.deleteButton = qt.QPushButton("×")
        self.deleteButton.setMaximumWidth(25)
        self.deleteButton.setMaximumHeight(25)
        self.deleteButton.setToolTip("删除此映射")
        self.deleteButton.setStyleSheet("QPushButton { color: red; font-weight: bold; }")
        self.deleteButton.clicked.connect(self.onDeleteClicked)
        layout.addWidget(self.deleteButton)

        layout.addStretch()

    def onMinHUChanged(self):
        if self.minHUSpinBox.value > self.maxHUSpinBox.value:
            self.maxHUSpinBox.setValue(self.minHUSpinBox.value)
        self.valueChanged.emit()

    def onMaxHUChanged(self):
        if self.maxHUSpinBox.value < self.minHUSpinBox.value:
            self.minHUSpinBox.setValue(self.maxHUSpinBox.value)
        self.valueChanged.emit()

    def chooseColor(self):
        color = qt.QColorDialog.getColor(self.getQColor())
        if color.isValid():
            self.setQColor(color)
            self.valueChanged.emit()

    def onDeleteClicked(self):
        self.deleteRequested.emit(self)

    def setMinHU(self, value):
        self.minHUSpinBox.setValue(value)

    def getMinHU(self):
        return self.minHUSpinBox.value

    def setMaxHU(self, value):
        self.maxHUSpinBox.setValue(value)

    def getMaxHU(self):
        return self.maxHUSpinBox.value

    def setColor(self, color_hex):
        color = qt.QColor(color_hex)
        self.setQColor(color)

    def setQColor(self, color):
        self.colorButton.setStyleSheet(f"background-color: {color.name()}")
        self._color = color

    def getQColor(self):
        return getattr(self, '_color', qt.QColor("#FF0000"))

    def getColor(self):
        return self.getQColor().name()

    def setName(self, name):
        self.nameEdit.setText(name)

    def getName(self):
        return self.nameEdit.text

#
# HUPseudoColorLogic - 核心逻辑类
#

class HUPseudoColorLogic(ScriptedLoadableModuleLogic):
    """HU伪彩的核心逻辑处理"""

    def __init__(self):
        ScriptedLoadableModuleLogic.__init__(self)
        self.previewVolume = None

    def updatePreview(self, inputVolume, colorMapping, smoothTransition=True):
        """更新预览"""
        logging.info(f"Preview update: {len(colorMapping)} color points, smooth={smoothTransition}")

    def applyColorMapping(self, inputVolume, colorMapping, smoothTransition=True, transparency=100):
        """应用颜色映射创建新体积"""
        try:
            # 获取输入数组
            inputArray = slicer.util.arrayFromVolume(inputVolume)

            # 创建颜色映射数组
            mappedArray = self.createColorMappedArray(inputArray, colorMapping, smoothTransition)

            # 创建输出体积
            outputVolume = slicer.mrmlScene.AddNewNodeByClass("vtkMRMLScalarVolumeNode")
            outputVolume.SetName(inputVolume.GetName() + "_HU伪彩映射")

            # 更新体积
            slicer.util.updateVolumeFromArray(outputVolume, mappedArray)
            outputVolume.CopyOrientation(inputVolume)

            # 创建并应用颜色表
            colorNode = self.createColorTable(colorMapping, smoothTransition, transparency)
            displayNode = outputVolume.GetDisplayNode()
            if not displayNode:
                displayNode = slicer.mrmlScene.AddNewNodeByClass("vtkMRMLScalarVolumeDisplayNode")
                outputVolume.SetAndObserveDisplayNodeID(displayNode.GetID())

            displayNode.SetAndObserveColorNodeID(colorNode.GetID())
            displayNode.SetWindowLevel(500, 250)

            return outputVolume

        except Exception as e:
            logging.error(f"Color mapping failed: {e}")
            return None

    def createColorMappedArray(self, inputArray, colorMapping, smoothTransition):
        """创建颜色映射数组"""
        mappedArray = np.zeros_like(inputArray, dtype=np.int16)

        print(f"创建颜色映射数组: {inputArray.shape}, 映射数量: {len(colorMapping)}")

        for i in range(inputArray.shape[0]):
            slice_data = inputArray[i]
            mapped_slice = np.zeros_like(slice_data, dtype=np.int16)

            # 区间映射 - 按顺序处理，后面的映射会覆盖前面的
            for j, mapping in enumerate(colorMapping):
                if mapping.get("type") == "range":
                    min_hu = mapping["min_hu"]
                    max_hu = mapping["max_hu"]

                    if min_hu > max_hu:
                        min_hu, max_hu = max_hu, min_hu

                    # 创建掩码
                    mask = (slice_data >= min_hu) & (slice_data <= max_hu)
                    
                    # 使用更简单的颜色索引，确保连续性
                    color_index = j + 1  # 从1开始，0保留给背景
                    mapped_slice[mask] = color_index

                    if i == 0:
                        pixel_count = np.sum(mask)
                        if pixel_count > 0:
                            print(f"  映射 {j+1}: {mapping.get('name', 'Unknown')} ({min_hu}~{max_hu} HU) -> 索引{color_index} -> {pixel_count} 像素")

            mappedArray[i] = mapped_slice

        print(f"映射完成，值范围: {np.min(mappedArray)} ~ {np.max(mappedArray)}")
        return mappedArray

    def createColorTable(self, colorMapping, smoothTransition, transparency=100):
        """创建颜色表"""
        colorNode = slicer.mrmlScene.AddNewNodeByClass("vtkMRMLColorTableNode")
        colorNode.SetName("HU伪彩_颜色表")
        colorNode.SetTypeToUser()

        max_colors = 256
        colorNode.SetNumberOfColors(max_colors)

        # 注意：这里不使用transparency参数，因为透明度通过显示方式控制
        print(f"创建颜色表: {len(colorMapping)} 个映射")

        # 初始化为黑色（不透明）
        for i in range(max_colors):
            colorNode.SetColor(i, 0, 0, 0, 1)

        # 设置用户定义的颜色 - 使用与createColorMappedArray相同的索引
        for i, mapping in enumerate(colorMapping):
            color = qt.QColor(mapping["color"])
            r, g, b = color.redF(), color.greenF(), color.blueF()

            color_index = i + 1  # 与createColorMappedArray保持一致
            if color_index < max_colors:
                colorNode.SetColor(color_index, r, g, b, 1.0)  # 完全不透明
                print(f"  颜色 {i+1}: {mapping.get('name', 'Unknown')} -> 索引 {color_index} -> RGB({r:.2f},{g:.2f},{b:.2f})")

        # 背景黑色
        colorNode.SetColor(0, 0.0, 0.0, 0.0, 1.0)

        print(f"颜色表创建完成")
        return colorNode

#
# 事件处理函数
#

def handle_input_changed(widget):
    """处理输入数据变化"""
    try:
        inputVolume = widget.inputSelector.currentNode()
        
        widget.applyButton.enabled = inputVolume is not None
        widget.refreshHUButton.enabled = inputVolume is not None

        if inputVolume:
            widget.statusLabel.setText(f"已选择输入: {inputVolume.GetName()}")
            widget.statusLabel.setStyleSheet("color: blue; font-weight: bold;")
            
            # 更新HU值信息
            update_hu_info(widget, inputVolume)
            
            if widget.realTimePreview.checked:
                widget.schedulePreviewUpdate()
        else:
            widget.statusLabel.setText("请选择输入数据")
            widget.statusLabel.setStyleSheet("color: orange; font-weight: bold;")
            clear_hu_info(widget)

    except Exception as e:
        print(f"输入变化处理失败: {e}")

def handle_transparency_changed(widget):
    """处理2D透明度变化 - 仅影响2D切片显示"""
    transparency_value = widget.transparencySlider.value
    widget.transparencyLabel.setText(f"{transparency_value}%")
    
    # 仅更新2D切片的透明度，不影响3D体积渲染
    update_2d_slice_transparency_only(widget, transparency_value)
    
    if widget.realTimePreview.checked:
        widget.schedulePreviewUpdate()

def handle_transparency_3d_changed(widget):
    """处理3D透明度变化"""
    transparency_value = widget.transparency3DSlider.value
    widget.transparency3DLabel.setText(f"{transparency_value}%")
    
    # 更新3D体积渲染的透明度
    update_3d_volume_transparency_only(widget, transparency_value)
    
    print(f"3D透明度已调整为: {transparency_value}%")

def handle_3d_display_changed(widget):
    """处理3D显示开关变化"""
    if widget.enable3DDisplay.checked:
        setup_3d_volume_rendering(widget)
    else:
        disable_3d_volume_rendering(widget)

def handle_save_config(widget):
    """处理保存配置"""
    try:
        colorMapping = widget.getColorMapping()
        
        result = qt.QInputDialog.getText(None, "保存配置", "请输入配置名称:")
        if isinstance(result, tuple):
            configName, ok = result
        else:
            configName = result
            ok = bool(configName)

        if not ok or not configName.strip():
            return

        result = qt.QFileDialog.getSaveFileName(None, "保存HU颜色映射配置",
                                                f"{configName.strip()}.json",
                                                "JSON Files (*.json)")
        if isinstance(result, tuple):
            fileName, _ = result
        else:
            fileName = result

        if not fileName:
            return

        config_data = {
            "name": configName.strip(),
            "description": f"HU颜色映射配置 - {configName.strip()}",
            "mappings": colorMapping
        }

        with open(fileName, 'w', encoding='utf-8') as f:
            json.dump(config_data, f, ensure_ascii=False, indent=2)

        slicer.util.infoDisplay(f"配置已保存到: {fileName}")
        print(f"配置已保存: {fileName}")

    except Exception as e:
        slicer.util.errorDisplay(f"保存配置失败: {e}")
        print(f"保存配置失败: {e}")

def handle_load_config(widget):
    """处理加载配置"""
    try:
        result = qt.QFileDialog.getOpenFileName(None, "加载HU颜色映射配置",
                                                "", "JSON Files (*.json)")
        if isinstance(result, tuple):
            fileName, _ = result
        else:
            fileName = result

        if not fileName:
            return

        with open(fileName, 'r', encoding='utf-8') as f:
            config_data = json.load(f)

        # 清除现有映射
        for mapping in widget.colorControlPoints:
            mapping.setParent(None)
        widget.colorControlPoints.clear()

        # 加载新映射
        mappings = config_data.get("mappings", [])
        for mapping in mappings:
            if mapping.get("type") == "range":
                widget.addColorMapping(
                    mapping.get("min_hu", 0),
                    mapping.get("max_hu", 100),
                    mapping.get("color", "#FF0000"),
                    mapping.get("name", "组织")
                )

        config_name = config_data.get("name", "未知配置")
        slicer.util.infoDisplay(f"配置已加载: {config_name}")
        print(f"配置已加载: {config_name}")

        if widget.realTimePreview.checked:
            widget.schedulePreviewUpdate()

    except Exception as e:
        slicer.util.errorDisplay(f"加载配置失败: {e}")
        print(f"加载配置失败: {e}")

def handle_remove_mapping(widget):
    """处理删除最后一个映射"""
    if len(widget.colorControlPoints) > 2:
        mapping = widget.colorControlPoints.pop()
        mapping.setParent(None)
        print(f"删除最后一个映射: {mapping.getName()}")
        if widget.realTimePreview.checked:
            widget.schedulePreviewUpdate()
    else:
        slicer.util.warningDisplay("至少需要保留2个映射范围")

def handle_delete_mapping(widget, mappingWidget):
    """处理删除指定映射"""
    if len(widget.colorControlPoints) > 2:
        if mappingWidget in widget.colorControlPoints:
            widget.colorControlPoints.remove(mappingWidget)
            mappingWidget.setParent(None)
            print(f"删除映射: {mappingWidget.getName()}")
            if widget.realTimePreview.checked:
                widget.schedulePreviewUpdate()
    else:
        slicer.util.warningDisplay("至少需要保留2个映射范围")

def handle_apply_mapping(widget):
    """处理应用颜色映射"""
    print("=== 开始应用颜色映射 ===")

    inputVolume = widget.inputSelector.currentNode()
    if not inputVolume:
        slicer.util.errorDisplay("请先选择输入的CT数据")
        return

    try:
        widget.statusLabel.setText("正在应用颜色映射...")
        widget.statusLabel.setStyleSheet("color: blue; font-weight: bold;")

        colorMapping = widget.getColorMapping()
        transparency = widget.transparencySlider.value
        
        outputVolume = widget.logic.applyColorMapping(
            inputVolume, colorMapping, widget.smoothTransition.checked, transparency
        )

        if outputVolume:
            # 强制刷新显示属性
            displayNode = outputVolume.GetDisplayNode()
            if displayNode:
                displayNode.SetAutoWindowLevel(False)
                displayNode.SetWindow(256)
                displayNode.SetLevel(128)
                displayNode.Modified()
                outputVolume.Modified()

            # 设置透明度显示
            setup_transparent_display(widget, outputVolume, inputVolume)

            # 如果启用了3D显示，设置体积渲染
            if widget.enable3DDisplay.checked:
                setup_3d_volume_rendering(widget)

            widget.statusLabel.setText("颜色映射完成")
            widget.statusLabel.setStyleSheet("color: green; font-weight: bold;")
            slicer.util.infoDisplay(f"颜色映射已应用!\n输出: {outputVolume.GetName()}")
        else:
            widget.statusLabel.setText("颜色映射失败")
            widget.statusLabel.setStyleSheet("color: red; font-weight: bold;")
            slicer.util.errorDisplay("颜色映射失败")

    except Exception as e:
        print(f"颜色映射异常: {e}")
        slicer.util.errorDisplay(f"颜色映射失败: {e}")
        widget.statusLabel.setText("处理失败")
        widget.statusLabel.setStyleSheet("color: red; font-weight: bold;")

def handle_refresh_hu_info(widget):
    """处理刷新HU值信息"""
    inputVolume = widget.inputSelector.currentNode()
    if inputVolume:
        update_hu_info(widget, inputVolume)
    else:
        clear_hu_info(widget)

#
# 辅助函数
#

def update_hu_info(widget, inputVolume):
    """更新HU值信息"""
    try:
        inputArray = slicer.util.arrayFromVolume(inputVolume)
        
        hu_min = float(np.min(inputArray))
        hu_max = float(np.max(inputArray))
        hu_mean = float(np.mean(inputArray))
        hu_std = float(np.std(inputArray))
        pixel_count = int(inputArray.size)

        widget.huMinLabel.setText(f"{hu_min:.1f} HU")
        widget.huMaxLabel.setText(f"{hu_max:.1f} HU")
        widget.huMeanLabel.setText(f"{hu_mean:.1f} HU")
        widget.huStdLabel.setText(f"{hu_std:.1f} HU")
        widget.pixelCountLabel.setText(f"{pixel_count:,}")

        print(f"HU值统计: {hu_min:.1f} ~ {hu_max:.1f} HU")

    except Exception as e:
        print(f"HU值分析失败: {e}")
        clear_hu_info(widget)

def clear_hu_info(widget):
    """清除HU值信息"""
    widget.huMinLabel.setText("--")
    widget.huMaxLabel.setText("--")
    widget.huMeanLabel.setText("--")
    widget.huStdLabel.setText("--")
    widget.pixelCountLabel.setText("--")

def setup_transparent_display(widget, outputVolume, inputVolume):
    """设置初始透明度显示 - 仅用于初始化，后续透明度调节通过独立函数处理"""
    try:
        transparency_percent = widget.transparencySlider.value
        # 透明度百分比直接作为伪彩的不透明度（0%=只看原始CT，100%=只看伪彩）
        opacity = transparency_percent / 100.0
        
        print(f"初始化2D透明度显示: {transparency_percent}%, 伪彩不透明度: {opacity:.2f}")
        
        # 设置前景/背景混合模式用于2D切片显示
        for viewName in ['Red', 'Yellow', 'Green']:
            sliceWidget = slicer.app.layoutManager().sliceWidget(viewName)
            if sliceWidget:
                sliceLogic = sliceWidget.sliceLogic()
                compositeNode = sliceLogic.GetSliceCompositeNode()
                
                if compositeNode:
                    # 原始CT作为背景
                    compositeNode.SetBackgroundVolumeID(inputVolume.GetID())
                    # 伪彩作为前景
                    compositeNode.SetForegroundVolumeID(outputVolume.GetID())
                    # 设置前景不透明度
                    compositeNode.SetForegroundOpacity(opacity)
                    compositeNode.SetCompositing(1)
                    compositeNode.Modified()
        
        print(f"✓ 2D透明度显示初始化完成 - 透明度{transparency_percent}%")
        
    except Exception as e:
        print(f"✗ 初始化透明度显示失败: {e}")

def setup_3d_volume_rendering(widget):
    """设置3D体积渲染 - 确保使用伪彩数据"""
    try:
        outputVolume = None
        for node in slicer.util.getNodesByClass('vtkMRMLScalarVolumeNode'):
            if 'HU伪彩映射' in node.GetName():
                outputVolume = node
                break
        
        if outputVolume:
            print(f"找到伪彩输出体积: {outputVolume.GetName()}")
            
            # 检查伪彩数据
            outputArray = slicer.util.arrayFromVolume(outputVolume)
            print(f"伪彩数据统计: 形状={outputArray.shape}, 范围={np.min(outputArray)}~{np.max(outputArray)}, 非零像素={np.count_nonzero(outputArray)}")
            
            # 先清除所有现有的体积渲染
            displayNodes = slicer.util.getNodesByClass('vtkMRMLVolumeRenderingDisplayNode')
            for displayNode in displayNodes:
                if displayNode.GetVolumeNode() == outputVolume:
                    slicer.mrmlScene.RemoveNode(displayNode)
                    print("清除现有体积渲染显示节点")
            
            # 创建新的体积渲染显示节点
            volRenLogic = slicer.modules.volumerendering.logic()
            displayNode = volRenLogic.CreateDefaultVolumeRenderingNodes(outputVolume)
            
            if displayNode:
                print(f"创建体积渲染显示节点: {displayNode.GetName()}")
                
                # 确保体积渲染可见
                displayNode.SetVisibility(True)
                
                # 设置体积渲染属性
                setup_volume_rendering_properties(widget, displayNode, outputVolume)
                
                # 强制刷新3D视图
                layoutManager = slicer.app.layoutManager()
                threeDWidget = layoutManager.threeDWidget(0)
                threeDView = threeDWidget.threeDView()
                threeDView.resetFocalPoint()
                threeDView.forceRender()
                
                print("✓ 3D伪彩体积渲染已启用并刷新")
            else:
                print("✗ 无法创建体积渲染显示节点")
        else:
            print("✗ 未找到HU伪彩映射输出体积")
        
    except Exception as e:
        print(f"✗ 设置3D体积渲染失败: {e}")
        import traceback
        traceback.print_exc()

def setup_volume_rendering_properties(widget, displayNode, outputVolume):
    """设置CT伪彩体积渲染属性 - 使用成功的修复逻辑"""
    try:
        print("=== 设置CT伪彩3D体积渲染 ===")
        
        # 获取体积属性
        volumePropertyNode = displayNode.GetVolumePropertyNode()
        if not volumePropertyNode:
            volumePropertyNode = slicer.mrmlScene.AddNewNodeByClass("vtkMRMLVolumePropertyNode")
            displayNode.SetAndObserveVolumePropertyNodeID(volumePropertyNode.GetID())
        
        volumeProperty = volumePropertyNode.GetVolumeProperty()
        
        # 获取伪彩数据的实际值范围
        outputArray = slicer.util.arrayFromVolume(outputVolume)
        data_min = float(np.min(outputArray))
        data_max = float(np.max(outputArray))
        print(f"伪彩数据值范围: {data_min} ~ {data_max}")
        
        # 获取用户的实际颜色映射配置
        try:
            user_color_mapping = widget.getColorMapping()
            print(f"获取用户颜色映射: {len(user_color_mapping)} 个")
            
            # 使用用户的实际颜色映射
            template_mappings = []
            for mapping in user_color_mapping:
                template_mappings.append({
                    "color": mapping.get("color", "#FF0000"),
                    "name": mapping.get("name", "Unknown")
                })
        except:
            print("无法获取用户颜色映射，使用默认模板")
            # 使用固定的模板颜色映射作为后备
            template_mappings = [
                {"color": "#000000", "name": "1"},    # 黑色
                {"color": "#4e0000", "name": "2"},    # 深红
                {"color": "#960000", "name": "3"},    # 红色
                {"color": "#ff0000", "name": "4"},    # 亮红
                {"color": "#ffa500", "name": "5"},    # 橙色
                {"color": "#ffff00", "name": "6"},    # 黄色
                {"color": "#9bff00", "name": "7"},    # 黄绿
                {"color": "#00ff00", "name": "8"},    # 绿色
                {"color": "#009b00", "name": "9"},    # 深绿
                {"color": "#006400", "name": "10"},   # 更深绿
                {"color": "#004221", "name": "11"},   # 最深绿
            ]
        
        print(f"使用模板颜色映射: {len(template_mappings)} 个")
        
        # 清除现有的传输函数
        colorTransferFunction = volumeProperty.GetRGBTransferFunction()
        colorTransferFunction.RemoveAllPoints()
        
        opacityTransferFunction = volumeProperty.GetScalarOpacity()
        opacityTransferFunction.RemoveAllPoints()
        
        # 背景透明
        colorTransferFunction.AddRGBPoint(0, 0.0, 0.0, 0.0)
        opacityTransferFunction.AddPoint(0, 0.0)
        print("  索引 0: RGB(0.00,0.00,0.00) 透明度=0.0 (背景)")
        
        # 设置模板颜色，使用独立的3D透明度滑块
        user_3d_transparency = widget.transparency3DSlider.value / 100.0
        print(f"使用3D独立透明度设置: {user_3d_transparency:.2f}")
        
        for i, mapping in enumerate(template_mappings):
            color_index = i + 1
            
            # 解析颜色
            color = qt.QColor(mapping["color"])
            r, g, b = color.redF(), color.greenF(), color.blueF()
            
            # 设置颜色
            colorTransferFunction.AddRGBPoint(color_index, r, g, b)
            
            # 3D透明度设置：0%=伪彩不透明，100%=伪彩透明
            if mapping.get("color", "#000000").upper() == "#000000":
                # 黑色完全透明
                opacity = 0.0
            else:
                # 其他颜色：透明度转换为不透明度
                # user_3d_transparency: 0%=伪彩不透明，100%=伪彩透明
                # 所以3D不透明度 = 1.0 - user_3d_transparency
                opacity = 1.0 - user_3d_transparency
                
                # 确保透明度在合理范围内，避免完全消失
                opacity = max(0.05, min(opacity, 0.95))
            
            opacityTransferFunction.AddPoint(color_index, opacity)
            
            print(f"  索引 {color_index}: {mapping['name']} ({mapping.get('color', '#000000')}) -> RGB({r:.2f},{g:.2f},{b:.2f}) 透明度={opacity:.2f}")
        
        # 体积属性设置（恢复到能显示的配置，稍微优化）
        volumeProperty.SetInterpolationTypeToLinear()
        volumeProperty.ShadeOff()  # 关闭阴影，确保基本可见性
        volumeProperty.SetAmbient(1.0)    # 最大环境光
        volumeProperty.SetDiffuse(0.0)    # 关闭漫反射
        volumeProperty.SetSpecular(0.0)   # 关闭镜面反射
        
        # 梯度不透明度设置（恢复到能显示的配置）
        gradientOpacity = volumeProperty.GetGradientOpacity()
        gradientOpacity.RemoveAllPoints()
        gradientOpacity.AddPoint(0, 1.0)    # 所有梯度都不透明，确保可见
        gradientOpacity.AddPoint(255, 1.0)
        
        # 强制更新显示
        volumePropertyNode.Modified()
        displayNode.Modified()
        
        # 确保体积渲染可见
        displayNode.SetVisibility(True)
        
        # 强制刷新3D视图
        layoutManager = slicer.app.layoutManager()
        threeDWidget = layoutManager.threeDWidget(0)
        threeDView = threeDWidget.threeDView()
        threeDView.resetFocalPoint()
        renderWindow = threeDView.renderWindow()
        renderWindow.Render()
        
        print("✓ CT伪彩3D体积渲染已设置（使用成功的配置）")
        
    except Exception as e:
        print(f"✗ 设置CT伪彩体积渲染失败: {e}")
        import traceback
        traceback.print_exc()

def disable_3d_volume_rendering(widget):
    """禁用3D体积渲染"""
    try:
        displayNodes = slicer.util.getNodesByClass('vtkMRMLVolumeRenderingDisplayNode')
        for displayNode in displayNodes:
            volumeNode = displayNode.GetVolumeNode()
            if volumeNode and 'HU伪彩映射' in volumeNode.GetName():
                displayNode.SetVisibility(False)
                print(f"已禁用 {volumeNode.GetName()} 的3D体积渲染")
                
    except Exception as e:
        print(f"禁用3D体积渲染失败: {e}")

def update_output_transparency(widget, transparency_percent):
    """更新输出透明度"""
    try:
        outputVolume = None
        inputVolume = widget.inputSelector.currentNode()
        
        for node in slicer.util.getNodesByClass('vtkMRMLScalarVolumeNode'):
            if 'HU伪彩映射' in node.GetName():
                outputVolume = node
                break
        
        if outputVolume and inputVolume:
            # 透明度百分比直接作为伪彩的不透明度（0%=只看原始CT，100%=只看伪彩）
            opacity = transparency_percent / 100.0
            print(f"更新透明度为: {transparency_percent}%, 伪彩不透明度: {opacity:.2f}")
            
            # 始终使用前景/背景混合模式，这样透明度调整更平滑
            for viewName in ['Red', 'Yellow', 'Green']:
                sliceWidget = slicer.app.layoutManager().sliceWidget(viewName)
                if sliceWidget:
                    sliceLogic = sliceWidget.sliceLogic()
                    compositeNode = sliceLogic.GetSliceCompositeNode()
                    if compositeNode:
                        # 原始CT作为背景
                        compositeNode.SetBackgroundVolumeID(inputVolume.GetID())
                        # 伪彩作为前景
                        compositeNode.SetForegroundVolumeID(outputVolume.GetID())
                        # 设置前景不透明度
                        compositeNode.SetForegroundOpacity(opacity)
                        compositeNode.SetCompositing(1)
                        compositeNode.Modified()
            
            print(f"2D透明度更新完成 - 透明度{transparency_percent}% = 伪彩不透明度{opacity:.2f}")
            
            print(f"2D透明度已更新为 {transparency_percent}%")
            
    except Exception as e:
        print(f"更新透明度失败: {e}")
        import traceback
        traceback.print_exc()

def update_2d_slice_transparency_only(widget, transparency_percent):
    """仅更新2D切片透明度，不影响3D体积渲染"""
    try:
        outputVolume = None
        inputVolume = widget.inputSelector.currentNode()
        
        for node in slicer.util.getNodesByClass('vtkMRMLScalarVolumeNode'):
            if 'HU伪彩映射' in node.GetName():
                outputVolume = node
                break
        
        if outputVolume and inputVolume:
            # 透明度百分比直接作为伪彩的不透明度（0%=只看原始CT，100%=只看伪彩）
            opacity = transparency_percent / 100.0
            print(f"仅更新2D切片透明度: {transparency_percent}%, 伪彩不透明度: {opacity:.2f}")
            
            # 仅更新2D切片视图的混合模式
            for viewName in ['Red', 'Yellow', 'Green']:
                sliceWidget = slicer.app.layoutManager().sliceWidget(viewName)
                if sliceWidget:
                    sliceLogic = sliceWidget.sliceLogic()
                    compositeNode = sliceLogic.GetSliceCompositeNode()
                    if compositeNode:
                        # 原始CT作为背景
                        compositeNode.SetBackgroundVolumeID(inputVolume.GetID())
                        # 伪彩作为前景
                        compositeNode.SetForegroundVolumeID(outputVolume.GetID())
                        # 设置前景不透明度
                        compositeNode.SetForegroundOpacity(opacity)
                        compositeNode.SetCompositing(1)
                        compositeNode.Modified()
            
            print(f"✓ 2D切片透明度已独立更新为 {transparency_percent}%")
            
    except Exception as e:
        print(f"✗ 更新2D切片透明度失败: {e}")
        import traceback
        traceback.print_exc()

def update_3d_volume_transparency_only(widget, transparency_percent):
    """仅更新3D体积渲染透明度，不影响2D切片显示"""
    try:
        outputVolume = None
        for node in slicer.util.getNodesByClass('vtkMRMLScalarVolumeNode'):
            if 'HU伪彩映射' in node.GetName():
                outputVolume = node
                break
        
        if not outputVolume:
            print("未找到HU伪彩映射输出体积，无法更新3D透明度")
            return
            
        displayNodes = slicer.util.getNodesByClass('vtkMRMLVolumeRenderingDisplayNode')
        for displayNode in displayNodes:
            if displayNode.GetVolumeNode() == outputVolume:
                volumePropertyNode = displayNode.GetVolumePropertyNode()
                if volumePropertyNode:
                    volumeProperty = volumePropertyNode.GetVolumeProperty()
                    opacityTransferFunction = volumeProperty.GetScalarOpacity()
                    
                    # 3D透明度：0%=伪彩不透明，100%=伪彩透明
                    user_3d_transparency = transparency_percent / 100.0
                    colorMapping = widget.getColorMapping()
                    
                    print(f"仅更新3D体积渲染透明度: {transparency_percent}% (3D透明度: {user_3d_transparency:.2f})")
                    
                    # 清除现有透明度点
                    opacityTransferFunction.RemoveAllPoints()
                    opacityTransferFunction.AddPoint(0, 0.0)  # 背景透明
                    
                    # 为每个伪彩颜色索引设置透明度
                    for i, mapping in enumerate(colorMapping):
                        color_index = i + 1  # 与createColorMappedArray保持一致
                        
                        # 3D透明度设置：0%=伪彩不透明，100%=伪彩透明
                        if mapping.get("color", "#000000").upper() == "#000000":
                            # 黑色完全透明
                            opacity = 0.0
                        else:
                            # 其他颜色：透明度转换为不透明度
                            # user_3d_transparency: 0%=伪彩不透明，100%=伪彩透明
                            # 所以3D不透明度 = 1.0 - user_3d_transparency
                            opacity = 1.0 - user_3d_transparency
                            
                            # 确保透明度在合理范围内，避免完全消失
                            opacity = max(0.05, min(opacity, 0.95))
                        
                        opacityTransferFunction.AddPoint(color_index, opacity)
                        print(f"  更新3D伪彩透明度 {i+1}: {mapping.get('name', 'Unknown')} -> 索引{color_index} -> 不透明度{opacity:.2f}")
                    
                    volumePropertyNode.Modified()
                    displayNode.Modified()
                    
                    # 强制刷新3D视图
                    layoutManager = slicer.app.layoutManager()
                    threeDWidget = layoutManager.threeDWidget(0)
                    threeDView = threeDWidget.threeDView()
                    renderWindow = threeDView.renderWindow()
                    renderWindow.Render()
                    
                    print("✓ 3D体积渲染透明度已独立更新")
                    break
                    
    except Exception as e:
        print(f"✗ 更新3D体积渲染透明度失败: {e}")
        import traceback
        traceback.print_exc()

#
# 便捷函数
#

def quickFixHURange():
    """快速修复HU值范围覆盖问题"""
    try:
        print("=== 快速修复HU值范围覆盖 ===")
        
        moduleWidget = slicer.modules.hupseudocolor.widgetRepresentation().self()
        if moduleWidget:
            inputVolume = moduleWidget.inputSelector.currentNode()
            if inputVolume:
                print(f"输入数据: {inputVolume.GetName()}")
                
                inputArray = slicer.util.arrayFromVolume(inputVolume)
                hu_min = np.min(inputArray)
                hu_max = np.max(inputArray)
                print(f"数据HU范围: {hu_min:.1f} ~ {hu_max:.1f}")
                
                colorMapping = moduleWidget.getColorMapping()
                print(f"当前映射数量: {len(colorMapping)}")
                
                # 应用映射
                handle_apply_mapping(moduleWidget)
                print("✅ 映射应用成功")
                
                return True
            else:
                print("✗ 没有输入数据")
                return False
        else:
            print("✗ 没有找到Widget")
            return False
            
    except Exception as e:
        print(f"✗ 快速修复失败: {e}")
        return False

def update_3d_volume_transparency_only(widget, transparency_percent):
    """仅更新3D体积渲染透明度，不影响2D显示"""
    try:
        outputVolume = None
        for node in slicer.util.getNodesByClass('vtkMRMLScalarVolumeNode'):
            if 'HU伪彩映射' in node.GetName():
                outputVolume = node
                break
        
        if not outputVolume:
            print("✗ 未找到HU伪彩映射输出体积")
            return
            
        displayNodes = slicer.util.getNodesByClass('vtkMRMLVolumeRenderingDisplayNode')
        for displayNode in displayNodes:
            if displayNode.GetVolumeNode() == outputVolume:
                volumePropertyNode = displayNode.GetVolumePropertyNode()
                if volumePropertyNode:
                    volumeProperty = volumePropertyNode.GetVolumeProperty()
                    
                    colorTransferFunction = volumeProperty.GetRGBTransferFunction()
                    opacityTransferFunction = volumeProperty.GetScalarOpacity()
                    
                    # 获取用户的颜色映射配置
                    try:
                        colorMapping = widget.getColorMapping()
                    except:
                        print("✗ 无法获取颜色映射配置")
                        return
                    
                    print(f"更新3D独立透明度: {transparency_percent}%")
                    
                    # 清除现有透明度点
                    opacityTransferFunction.RemoveAllPoints()
                    opacityTransferFunction.AddPoint(0, 0.0)  # 背景透明
                    
                    # 为每个颜色索引设置透明度
                    for i, mapping in enumerate(colorMapping):
                        color_index = i + 1
                        
                        # 3D透明度逻辑：0%=伪彩不透明，100%=伪彩透明
                        if mapping.get("color", "#000000").upper() == "#000000":
                            # 黑色完全透明
                            opacity = 0.0
                        else:
                            # 其他颜色：透明度转换为不透明度
                            # transparency_percent: 0%=伪彩不透明，100%=伪彩透明
                            # 所以3D不透明度 = (100 - transparency_percent) / 100
                            opacity = (100 - transparency_percent) / 100.0
                            
                            # 确保透明度在合理范围内
                            opacity = max(0.05, min(opacity, 0.95))
                        
                        opacityTransferFunction.AddPoint(color_index, opacity)
                        print(f"  3D索引 {color_index}: {mapping.get('name', 'Unknown')} -> 透明度{opacity:.2f}")
                    
                    # 更新显示
                    volumePropertyNode.Modified()
                    displayNode.Modified()
                    
                    # 强制刷新3D视图
                    layoutManager = slicer.app.layoutManager()
                    threeDWidget = layoutManager.threeDWidget(0)
                    threeDView = threeDWidget.threeDView()
                    renderWindow = threeDView.renderWindow()
                    renderWindow.Render()
                    
                    print("✓ 3D独立透明度已更新")
                    break
                    
    except Exception as e:
        print(f"✗ 更新3D独立透明度失败: {e}")
        import traceback
        traceback.print_exc()

#
# 便捷函数
#

def quickFixHURange():
    """快速修复HU值范围覆盖问题"""
    try:
        print("=== 快速修复HU值范围覆盖 ===")
        
        moduleWidget = slicer.modules.hupseudocolor.widgetRepresentation().self()
        if moduleWidget:
            inputVolume = moduleWidget.inputSelector.currentNode()
            if inputVolume:
                print(f"输入数据: {inputVolume.GetName()}")
                
                inputArray = slicer.util.arrayFromVolume(inputVolume)
                hu_min = np.min(inputArray)
                hu_max = np.max(inputArray)
                print(f"数据HU范围: {hu_min:.1f} ~ {hu_max:.1f}")
                
                colorMapping = moduleWidget.getColorMapping()
                print(f"当前映射数量: {len(colorMapping)}")
                
                # 应用映射
                handle_apply_mapping(moduleWidget)
                print("✅ 映射应用成功")
                
                return True
            else:
                print("✗ 没有输入数据")
                return False
        else:
            print("✗ 没有找到Widget")
            return False
            
    except Exception as e:
        print(f"✗ 快速修复失败: {e}")
        return False