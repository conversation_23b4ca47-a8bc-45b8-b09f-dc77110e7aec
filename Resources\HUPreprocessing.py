"""
HU值预处理工具模块
提供各种HU值映射和预处理方法
"""

import numpy as np
from typing import Tuple, Optional
import slicer
import vtk


class HUPreprocessor:
    """HU值预处理器类"""
    
    def __init__(self):
        """初始化预处理器"""
        pass
    
    def custom_range_mapping(self, source_array: np.ndarray, 
                           target_range: Tuple[float, float] = (-1000, 3000),
                           source_range: Optional[Tuple[float, float]] = None,
                           method: str = 'linear') -> np.ndarray:
        """
        自定义区间的HU值映射
        
        参数:
            source_array: 源CT数据数组
            target_range: 目标映射区间 (min, max)
            source_range: 源数据区间，如果为None则自动计算
            method: 映射方法 ('linear', 'percentile', 'zscore')
        
        返回:
            映射后的CT数据数组
        """
        print(f"应用自定义区间映射 (方法: {method})...")
        
        # 获取源数据范围
        if source_range is None:
            source_min = np.min(source_array)
            source_max = np.max(source_array)
        else:
            source_min, source_max = source_range
        
        target_min, target_max = target_range
        
        print(f"源数据范围: [{source_min:.2f}, {source_max:.2f}]")
        print(f"目标范围: [{target_min:.2f}, {target_max:.2f}]")
        
        if method == 'linear':
            mapped_array = self._linear_mapping(source_array, source_min, source_max, 
                                              target_min, target_max)
        elif method == 'percentile':
            mapped_array = self._percentile_mapping(source_array, target_min, target_max)
        elif method == 'zscore':
            mapped_array = self._zscore_mapping(source_array, target_min, target_max)
        else:
            raise ValueError(f"不支持的映射方法: {method}")
        
        # 打印映射后的统计信息
        self._print_mapping_stats(source_array, mapped_array)
        
        return mapped_array
    
    def _linear_mapping(self, source_array: np.ndarray, 
                       source_min: float, source_max: float,
                       target_min: float, target_max: float) -> np.ndarray:
        """线性映射"""
        if source_max == source_min:
            # 避免除零错误
            return np.full_like(source_array, (target_min + target_max) / 2)
        
        # 线性映射公式: y = (x - x_min) / (x_max - x_min) * (y_max - y_min) + y_min
        mapped_array = ((source_array - source_min) / (source_max - source_min) * 
                       (target_max - target_min) + target_min)
        
        return mapped_array
    
    def _percentile_mapping(self, source_array: np.ndarray, 
                           target_min: float, target_max: float,
                           percentile_range: Tuple[float, float] = (1, 99)) -> np.ndarray:
        """基于百分位数的映射"""
        p_low, p_high = percentile_range
        source_p_low = np.percentile(source_array, p_low)
        source_p_high = np.percentile(source_array, p_high)
        
        print(f"使用百分位数范围: {p_low}%-{p_high}% [{source_p_low:.2f}, {source_p_high:.2f}]")
        
        return self._linear_mapping(source_array, source_p_low, source_p_high, 
                                  target_min, target_max)
    
    def _zscore_mapping(self, source_array: np.ndarray, 
                       target_min: float, target_max: float,
                       sigma_range: float = 3.0) -> np.ndarray:
        """基于Z-score的映射"""
        source_mean = np.mean(source_array)
        source_std = np.std(source_array)
        
        # 使用均值±N个标准差作为源范围
        source_min = source_mean - sigma_range * source_std
        source_max = source_mean + sigma_range * source_std
        
        print(f"使用Z-score范围: ±{sigma_range}σ [{source_min:.2f}, {source_max:.2f}]")
        
        return self._linear_mapping(source_array, source_min, source_max, 
                                  target_min, target_max)
    
    def window_level_mapping(self, source_array: np.ndarray,
                           window_width: float, window_level: float,
                           target_range: Tuple[float, float] = (0, 255)) -> np.ndarray:
        """
        窗宽窗位映射
        
        参数:
            source_array: 源CT数据数组
            window_width: 窗宽
            window_level: 窗位
            target_range: 目标映射区间
        
        返回:
            映射后的数据数组
        """
        print(f"应用窗宽窗位映射 (窗宽: {window_width}, 窗位: {window_level})...")
        
        # 计算窗口范围
        window_min = window_level - window_width / 2
        window_max = window_level + window_width / 2
        
        print(f"窗口范围: [{window_min:.2f}, {window_max:.2f}]")
        
        # 应用窗宽窗位
        windowed_array = np.clip(source_array, window_min, window_max)
        
        # 映射到目标范围
        target_min, target_max = target_range
        mapped_array = self._linear_mapping(windowed_array, window_min, window_max,
                                          target_min, target_max)
        
        self._print_mapping_stats(source_array, mapped_array)
        
        return mapped_array
    
    def adaptive_mapping(self, source_array: np.ndarray,
                        target_range: Tuple[float, float] = (-1000, 3000),
                        outlier_percentile: float = 1.0) -> np.ndarray:
        """
        自适应映射，自动去除异常值
        
        参数:
            source_array: 源CT数据数组
            target_range: 目标映射区间
            outlier_percentile: 异常值百分位数阈值
        
        返回:
            映射后的CT数据数组
        """
        print(f"应用自适应映射 (异常值阈值: {outlier_percentile}%)...")
        
        # 计算有效数据范围（去除异常值）
        valid_min = np.percentile(source_array, outlier_percentile)
        valid_max = np.percentile(source_array, 100 - outlier_percentile)
        
        print(f"有效数据范围: [{valid_min:.2f}, {valid_max:.2f}]")
        
        # 应用线性映射
        target_min, target_max = target_range
        mapped_array = self._linear_mapping(source_array, valid_min, valid_max,
                                          target_min, target_max)
        
        # 裁剪到目标范围
        mapped_array = np.clip(mapped_array, target_min, target_max)
        
        self._print_mapping_stats(source_array, mapped_array)
        
        return mapped_array
    
    def piecewise_linear_mapping(self, source_array: np.ndarray,
                               breakpoints: list,
                               target_values: list) -> np.ndarray:
        """
        分段线性映射
        
        参数:
            source_array: 源CT数据数组
            breakpoints: 分段点列表 [x1, x2, x3, ...]
            target_values: 对应的目标值列表 [y1, y2, y3, ...]
        
        返回:
            映射后的CT数据数组
        """
        print(f"应用分段线性映射...")
        print(f"分段点: {breakpoints}")
        print(f"目标值: {target_values}")
        
        if len(breakpoints) != len(target_values):
            raise ValueError("分段点和目标值的数量必须相等")
        
        # 使用numpy的interp函数进行分段线性插值
        mapped_array = np.interp(source_array, breakpoints, target_values)
        
        self._print_mapping_stats(source_array, mapped_array)
        
        return mapped_array
    
    def _print_mapping_stats(self, source_array: np.ndarray, mapped_array: np.ndarray):
        """打印映射前后的统计信息"""
        # 源数据统计
        source_mean = np.mean(source_array)
        source_std = np.std(source_array)
        source_min = np.min(source_array)
        source_max = np.max(source_array)
        
        # 映射后统计
        mapped_mean = np.mean(mapped_array)
        mapped_std = np.std(mapped_array)
        mapped_min = np.min(mapped_array)
        mapped_max = np.max(mapped_array)
        
        print(f"源数据统计: 均值={source_mean:.2f}, 标准差={source_std:.2f}, 范围=[{source_min:.2f}, {source_max:.2f}]")
        print(f"映射后统计: 均值={mapped_mean:.2f}, 标准差={mapped_std:.2f}, 范围=[{mapped_min:.2f}, {mapped_max:.2f}]")


# 便捷函数
def create_hu_preprocessor() -> HUPreprocessor:
    """创建HU预处理器实例"""
    return HUPreprocessor()


def quick_range_mapping(source_array: np.ndarray, 
                       target_range: Tuple[float, float] = (-1000, 3000),
                       method: str = 'linear') -> np.ndarray:
    """
    快速区间映射函数
    
    参数:
        source_array: 源CT数据数组
        target_range: 目标映射区间
        method: 映射方法
    
    返回:
        映射后的CT数据数组
    """
    preprocessor = HUPreprocessor()
    return preprocessor.custom_range_mapping(source_array, target_range, method=method)


# UI相关方法 - 用于3D Slicer界面操作
def onOriginSetting(widget):
    """原点设置按钮处理 - 将CT数据移动到第一象限"""
    inputVolume = widget.inputSelector.currentNode() if hasattr(widget, 'inputSelector') else None
    if not inputVolume:
        print("⚠️ 请先选择输入的CT数据")
        if hasattr(widget, 'statusLabel'):
            widget.statusLabel.setText("请先选择CT数据")
            widget.statusLabel.setStyleSheet("color: orange; font-weight: bold;")
        return
    
    try:
        if hasattr(widget, 'statusLabel'):
            widget.statusLabel.setText("正在计算并移动原点...")
            widget.statusLabel.setStyleSheet("color: blue; font-weight: bold;")

        # 执行原点移动
        result = moveToFirstQuadrant(widget, inputVolume)

        if result:
            if hasattr(widget, 'statusLabel'):
                widget.statusLabel.setText("原点设置完成")
                widget.statusLabel.setStyleSheet("color: green; font-weight: bold;")

            # 刷新CT切片视图
            refreshCTSliceViews(widget)

            # 去掉弹窗，只保留控制台输出
            print("✓ 原点设置完成！CT数据已移动到第一象限，所有坐标现在都大于0。")
        else:
            if hasattr(widget, 'statusLabel'):
                widget.statusLabel.setText("原点设置失败")
                widget.statusLabel.setStyleSheet("color: red; font-weight: bold;")

    except Exception as e:
        print(f"❌ 原点设置失败: {e}")
        if hasattr(widget, 'statusLabel'):
            widget.statusLabel.setText("处理失败")
            widget.statusLabel.setStyleSheet("color: red; font-weight: bold;")


def moveToFirstQuadrant(widget, inputVolume):
    """将CT数据移动到第一象限"""
    try:
        import numpy as np
        import vtk
        
        # 获取当前的几何信息
        origin = inputVolume.GetOrigin()
        spacing = inputVolume.GetSpacing()
        dimensions = inputVolume.GetImageData().GetDimensions()
        
        print(f"原始信息:")
        print(f"  原点: ({origin[0]:.2f}, {origin[1]:.2f}, {origin[2]:.2f})")
        print(f"  间距: ({spacing[0]:.2f}, {spacing[1]:.2f}, {spacing[2]:.2f})")
        print(f"  维度: ({dimensions[0]}, {dimensions[1]}, {dimensions[2]})")
        
        # 计算8个顶点的坐标
        vertices = []
        for i in range(2):
            for j in range(2):
                for k in range(2):
                    x = origin[0] + i * (dimensions[0] - 1) * spacing[0]
                    y = origin[1] + j * (dimensions[1] - 1) * spacing[1]
                    z = origin[2] + k * (dimensions[2] - 1) * spacing[2]
                    vertices.append([x, y, z])
        
        # 找到每个轴的最小值
        vertices = np.array(vertices)
        min_coords = np.min(vertices, axis=0)
        
        print(f"8个顶点坐标:")
        for i, vertex in enumerate(vertices):
            print(f"  顶点{i+1}: ({vertex[0]:.2f}, {vertex[1]:.2f}, {vertex[2]:.2f})")
        print(f"最小坐标: ({min_coords[0]:.2f}, {min_coords[1]:.2f}, {min_coords[2]:.2f})")
        
        # 计算需要移动的偏移量，确保最小值移动到一个小的正值（比如1.0）
        # 这样整个数据体都会在第一象限，且有一定的边距
        margin = 1.0  # 边距，确保所有坐标都大于0
        offset = -min_coords + margin
        print(f"计算偏移量: ({offset[0]:.2f}, {offset[1]:.2f}, {offset[2]:.2f})")

        # 计算新的原点
        new_origin = [origin[0] + offset[0],
                     origin[1] + offset[1],
                     origin[2] + offset[2]]
        print(f"新原点: ({new_origin[0]:.2f}, {new_origin[1]:.2f}, {new_origin[2]:.2f})")
        
        # 创建新的输出体积
        outputVolume = slicer.mrmlScene.AddNewNodeByClass("vtkMRMLScalarVolumeNode")
        outputVolume.SetName(f"{inputVolume.GetName()}_FirstQuadrant")
        
        # 复制数据
        volumeArray = slicer.util.arrayFromVolume(inputVolume)
        slicer.util.updateVolumeFromArray(outputVolume, volumeArray)
        
        # 设置新的几何信息
        outputVolume.SetOrigin(new_origin)
        outputVolume.SetSpacing(spacing)
        
        # 复制方向矩阵
        directions = vtk.vtkMatrix4x4()
        inputVolume.GetIJKToRASDirectionMatrix(directions)
        outputVolume.SetIJKToRASDirectionMatrix(directions)
        
        # 验证新的顶点坐标
        new_vertices = []
        for i in range(2):
            for j in range(2):
                for k in range(2):
                    x = new_origin[0] + i * (dimensions[0] - 1) * spacing[0]
                    y = new_origin[1] + j * (dimensions[1] - 1) * spacing[1]
                    z = new_origin[2] + k * (dimensions[2] - 1) * spacing[2]
                    new_vertices.append([x, y, z])
        
        new_vertices = np.array(new_vertices)
        new_min_coords = np.min(new_vertices, axis=0)
        new_max_coords = np.max(new_vertices, axis=0)
        
        print(f"移动后验证:")
        print(f"  新的最小坐标: ({new_min_coords[0]:.2f}, {new_min_coords[1]:.2f}, {new_min_coords[2]:.2f})")
        print(f"  新的最大坐标: ({new_max_coords[0]:.2f}, {new_max_coords[1]:.2f}, {new_max_coords[2]:.2f})")
        
        # 检查是否成功移动到第一象限（所有坐标都应该大于0）
        if np.all(new_min_coords > 0.5):  # 确保所有坐标都明显大于0
            print("✓ 成功移动到第一象限，所有坐标都大于0")

            # 计算数据体的中心坐标
            center_coords = (new_min_coords + new_max_coords) / 2
            print(f"  数据体中心坐标: ({center_coords[0]:.2f}, {center_coords[1]:.2f}, {center_coords[2]:.2f})")

            # 自动选择新创建的体积作为输入
            if hasattr(widget, 'inputSelector'):
                widget.inputSelector.setCurrentNode(outputVolume)
            return True
        else:
            print("✗ 移动失败，仍有坐标不在第一象限")
            print(f"  最小坐标: ({new_min_coords[0]:.2f}, {new_min_coords[1]:.2f}, {new_min_coords[2]:.2f})")
            return False
            
    except Exception as e:
        print(f"移动到第一象限失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def refreshCTSliceViews(widget):
    """刷新CT切片视图以显示新的CT数据"""
    try:
        print("\n" + "="*50)
        print("刷新CT切片视图")
        print("="*50)
        
        # 获取当前选中的体积节点
        currentVolume = widget.inputSelector.currentNode() if hasattr(widget, 'inputSelector') else None
        if not currentVolume:
            print("✗ 没有选中的体积节点")
            return False
        
        print(f"✓ 当前体积节点: {currentVolume.GetName()}")
        
        # 方法1: 温和地刷新切片视图，避免重置头位调整
        layoutManager = slicer.app.layoutManager()
        if layoutManager:
            print("正在刷新切片视图...")
            # layoutManager.resetSliceViews()  # 注释掉，避免重置头位调整
            print("✓ 切片视图刷新准备完成")
        
        # 方法2: 设置背景体积并刷新
        print("正在设置背景体积...")
        compositeNodes = slicer.util.getNodesByClass('vtkMRMLSliceCompositeNode')
        for compositeNode in compositeNodes:
            compositeNode.SetBackgroundVolumeID(currentVolume.GetID())
            print(f"✓ 已设置 {compositeNode.GetName()} 的背景体积")
        
        # 方法3: 调整窗口/级别以优化显示
        print("正在优化显示窗口...")
        displayNode = currentVolume.GetDisplayNode()
        if displayNode:
            # 自动计算合适的窗口/级别
            imageData = currentVolume.GetImageData()
            if imageData:
                scalarRange = imageData.GetScalarRange()
                window = scalarRange[1] - scalarRange[0]
                level = (scalarRange[1] + scalarRange[0]) / 2.0
                displayNode.SetWindow(window)
                displayNode.SetLevel(level)
                displayNode.SetAutoWindowLevel(True)
                print(f"✓ 已设置窗口/级别: 窗口={window:.1f}, 级别={level:.1f}")
        
        # 方法4: 适配切片到体积
        print("正在适配切片到体积...")
        sliceNames = ['Red', 'Yellow', 'Green']
        for sliceName in sliceNames:
            sliceWidget = layoutManager.sliceWidget(sliceName)
            if sliceWidget:
                sliceLogic = sliceWidget.sliceLogic()
                if sliceLogic:
                    sliceLogic.FitSliceToAll()
                    print(f"✓ 已适配 {sliceName} 切片")
        
        # 方法5: 强制刷新渲染
        print("正在强制刷新渲染...")
        for sliceName in sliceNames:
            sliceWidget = layoutManager.sliceWidget(sliceName)
            if sliceWidget:
                sliceView = sliceWidget.sliceView()
                if sliceView:
                    sliceView.scheduleRender()
                    print(f"✓ 已刷新 {sliceName} 切片渲染")
        
        # 方法6: 更新3D视图（如果需要）
        print("正在更新3D视图...")
        threeDWidget = layoutManager.threeDWidget(0)
        if threeDWidget:
            threeDView = threeDWidget.threeDView()
            if threeDView:
                threeDView.resetFocalPoint()
                threeDView.scheduleRender()
                print("✓ 已更新3D视图")
        
        # 方法7: 触发场景更新
        print("正在触发场景更新...")
        slicer.mrmlScene.Modified()
        
        print("✓ CT切片视图刷新完成")
        print("="*50)
        return True
        
    except Exception as e:
        print(f"✗ 刷新CT切片视图失败: {e}")
        import traceback
        traceback.print_exc()
        return False