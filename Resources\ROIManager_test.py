"""
ROI管理器测试版本 - 不依赖slicer模块
"""

def onCreateROI(widget):
    """创建ROI的便捷函数"""
    print("onCreateROI called")

def onCenterROI(widget):
    """居中ROI的便捷函数"""
    print("onCenterROI called")

def onRefreshROIList(widget):
    """刷新ROI列表的便捷函数"""
    print("onRefreshROIList called")

def onROISelectionChanged(widget):
    """ROI选择变化的便捷函数"""
    print("onROISelectionChanged called")

def onDeleteSelectedROI(widget):
    """删除选中ROI的便捷函数"""
    print("onDeleteSelectedROI called")

def onApplyROIAnalysis(widget):
    """应用ROI分析的便捷函数"""
    print("onApplyROIAnalysis called")

def onCreateGroup(widget):
    """创建分组的便捷函数"""
    print("onCreateGroup called")

def onAddToGroup(widget):
    """添加到分组的便捷函数"""
    print("onAddToGroup called")

def onRemoveFromGroup(widget):
    """从分组移除的便捷函数"""
    print("onRemoveFromGroup called")

def onAnalyzeGroup(widget):
    """分析分组的便捷函数"""
    print("onAnalyzeGroup called")