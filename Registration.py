import logging
import os

import slicer
from slicer.ScriptedLoadableModule import *

import qt
import ctk

#
# Registration
#

class Registration(ScriptedLoadableModule):
    """配准模块"""

    def __init__(self, parent):
        ScriptedLoadableModule.__init__(self, parent)
        self.parent.title = "配准"
        self.parent.categories = ["TeethLink--JiuYuan"]
        self.parent.dependencies = []
        self.parent.contributors = ["JiuYuan Development Team"]
        self.parent.helpText = """
图像配准功能
"""

#
# RegistrationWidget
#

class RegistrationWidget(ScriptedLoadableModuleWidget):
    """配准界面"""

    def __init__(self, parent=None):
        ScriptedLoadableModuleWidget.__init__(self, parent)
        self.logic = None

    def setup(self):
        ScriptedLoadableModuleWidget.setup(self)
        self.logic = RegistrationLogic()

        # Title
        titleLabel = qt.QLabel("配准")
        titleLabel.setStyleSheet("font-size: 18px; font-weight: bold; color: #4ECDC4; margin: 10px;")
        self.layout.addWidget(titleLabel)

        # Input section
        inputCollapsibleButton = ctk.ctkCollapsibleButton()
        inputCollapsibleButton.text = "输入数据"
        self.layout.addWidget(inputCollapsibleButton)
        inputFormLayout = qt.QFormLayout(inputCollapsibleButton)

        # Fixed volume selector
        self.fixedSelector = slicer.qMRMLNodeComboBox()
        self.fixedSelector.nodeTypes = ["vtkMRMLScalarVolumeNode"]
        self.fixedSelector.selectNodeUponCreation = True
        self.fixedSelector.addEnabled = False
        self.fixedSelector.removeEnabled = False
        self.fixedSelector.noneEnabled = False
        self.fixedSelector.showHidden = False
        self.fixedSelector.showChildNodeTypes = False
        self.fixedSelector.setMRMLScene(slicer.mrmlScene)
        self.fixedSelector.setToolTip("选择固定图像")
        inputFormLayout.addRow("固定图像: ", self.fixedSelector)

        # Moving volume selector
        self.movingSelector = slicer.qMRMLNodeComboBox()
        self.movingSelector.nodeTypes = ["vtkMRMLScalarVolumeNode"]
        self.movingSelector.selectNodeUponCreation = True
        self.movingSelector.addEnabled = False
        self.movingSelector.removeEnabled = False
        self.movingSelector.noneEnabled = False
        self.movingSelector.showHidden = False
        self.movingSelector.showChildNodeTypes = False
        self.movingSelector.setMRMLScene(slicer.mrmlScene)
        self.movingSelector.setToolTip("选择移动图像")
        inputFormLayout.addRow("移动图像: ", self.movingSelector)

        # Process button
        self.processButton = qt.QPushButton("开始配准")
        self.processButton.toolTip = "开始图像配准"
        self.processButton.enabled = False
        self.layout.addWidget(self.processButton)

        # Status
        self.statusLabel = qt.QLabel("就绪 - 请选择输入数据")
        self.statusLabel.setStyleSheet("color: green; font-weight: bold; margin: 10px;")
        self.layout.addWidget(self.statusLabel)

        # Connections
        self.processButton.connect('clicked(bool)', self.onProcessButton)
        self.fixedSelector.connect("currentNodeChanged(vtkMRMLNode*)", self.onInputChanged)
        self.movingSelector.connect("currentNodeChanged(vtkMRMLNode*)", self.onInputChanged)

        # Add vertical spacer
        self.layout.addStretch(1)

    def onInputChanged(self):
        fixedVolume = self.fixedSelector.currentNode()
        movingVolume = self.movingSelector.currentNode()
        self.processButton.enabled = fixedVolume is not None and movingVolume is not None
        
        if fixedVolume and movingVolume:
            self.statusLabel.setText(f"已选择: 固定图像 {fixedVolume.GetName()}, 移动图像 {movingVolume.GetName()}")
            self.statusLabel.setStyleSheet("color: blue; font-weight: bold;")
        else:
            self.statusLabel.setText("请选择固定图像和移动图像")
            self.statusLabel.setStyleSheet("color: orange; font-weight: bold;")

    def onProcessButton(self):
        fixedVolume = self.fixedSelector.currentNode()
        movingVolume = self.movingSelector.currentNode()
        
        if fixedVolume and movingVolume:
            try:
                self.statusLabel.setText("正在进行配准...")
                self.statusLabel.setStyleSheet("color: blue; font-weight: bold;")
                
                result = self.logic.performRegistration(fixedVolume, movingVolume)
                
                if result:
                    self.statusLabel.setText("配准完成")
                    self.statusLabel.setStyleSheet("color: green; font-weight: bold;")
                    slicer.util.infoDisplay("图像配准完成")
                else:
                    self.statusLabel.setText("配准失败")
                    self.statusLabel.setStyleSheet("color: red; font-weight: bold;")
                    
            except Exception as e:
                slicer.util.errorDisplay(f"配准失败: {e}")
                self.statusLabel.setText("处理失败")
                self.statusLabel.setStyleSheet("color: red; font-weight: bold;")

    def cleanup(self):
        pass

#
# RegistrationLogic
#

class RegistrationLogic(ScriptedLoadableModuleLogic):
    """配准逻辑"""

    def __init__(self):
        ScriptedLoadableModuleLogic.__init__(self)

    def performRegistration(self, fixedVolume, movingVolume):
        """执行配准"""
        logging.info("开始图像配准...")
        
        try:
            # Get volume information
            logging.info(f"固定图像: {fixedVolume.GetName()}")
            logging.info(f"移动图像: {movingVolume.GetName()}")
            
            # TODO: Implement actual registration algorithm
            # For now, just show success message
            
            logging.info("图像配准完成")
            return True
            
        except Exception as e:
            logging.error(f"配准失败: {e}")
            return False

#
# RegistrationTest
#

class RegistrationTest(ScriptedLoadableModuleTest):
    def setUp(self):
        slicer.mrmlScene.Clear()

    def runTest(self):
        self.setUp()
        self.test_Registration1()

    def test_Registration1(self):
        self.delayDisplay("开始测试")
        logic = RegistrationLogic()
        self.delayDisplay("测试完成")
