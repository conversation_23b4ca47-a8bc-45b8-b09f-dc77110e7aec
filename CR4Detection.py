import logging
import os

import slicer
from slicer.ScriptedLoadableModule import *

import qt
import ctk

#
# CR4Detection
#

class CR4Detection(ScriptedLoadableModule):
    """CR4识别点模块"""

    def __init__(self, parent):
        ScriptedLoadableModule.__init__(self, parent)
        self.parent.title = "CR4识别点"
        self.parent.categories = ["TeethLink--JiuYuan"]
        self.parent.dependencies = []
        self.parent.contributors = ["JiuYuan Development Team"]
        self.parent.helpText = """
CR4关键点自动识别功能
"""

#
# CR4DetectionWidget
#

class CR4DetectionWidget(ScriptedLoadableModuleWidget):
    """CR4识别点界面"""

    def __init__(self, parent=None):
        ScriptedLoadableModuleWidget.__init__(self, parent)
        self.logic = None

    def setup(self):
        ScriptedLoadableModuleWidget.setup(self)
        self.logic = CR4DetectionLogic()

        # Title
        titleLabel = qt.QLabel("CR4识别点")
        titleLabel.setStyleSheet("font-size: 18px; font-weight: bold; color: #FF6B6B; margin: 10px;")
        self.layout.addWidget(titleLabel)

        # Input section
        inputCollapsibleButton = ctk.ctkCollapsibleButton()
        inputCollapsibleButton.text = "输入数据"
        self.layout.addWidget(inputCollapsibleButton)
        inputFormLayout = qt.QFormLayout(inputCollapsibleButton)

        # Input volume selector
        self.inputSelector = slicer.qMRMLNodeComboBox()
        self.inputSelector.nodeTypes = ["vtkMRMLScalarVolumeNode"]
        self.inputSelector.selectNodeUponCreation = True
        self.inputSelector.addEnabled = False
        self.inputSelector.removeEnabled = False
        self.inputSelector.noneEnabled = False
        self.inputSelector.showHidden = False
        self.inputSelector.showChildNodeTypes = False
        self.inputSelector.setMRMLScene(slicer.mrmlScene)
        self.inputSelector.setToolTip("选择输入的CT数据")
        inputFormLayout.addRow("输入数据: ", self.inputSelector)

        # Process button
        self.processButton = qt.QPushButton("开始CR4识别")
        self.processButton.toolTip = "开始CR4关键点识别"
        self.processButton.enabled = False
        self.layout.addWidget(self.processButton)

        # Status
        self.statusLabel = qt.QLabel("就绪 - 请选择输入数据")
        self.statusLabel.setStyleSheet("color: green; font-weight: bold; margin: 10px;")
        self.layout.addWidget(self.statusLabel)

        # Connections
        self.processButton.connect('clicked(bool)', self.onProcessButton)
        self.inputSelector.connect("currentNodeChanged(vtkMRMLNode*)", self.onInputChanged)

        # Add vertical spacer
        self.layout.addStretch(1)

    def onInputChanged(self):
        inputVolume = self.inputSelector.currentNode()
        self.processButton.enabled = inputVolume is not None
        if inputVolume:
            self.statusLabel.setText(f"已选择输入: {inputVolume.GetName()}")
            self.statusLabel.setStyleSheet("color: blue; font-weight: bold;")
        else:
            self.statusLabel.setText("请选择输入数据")
            self.statusLabel.setStyleSheet("color: orange; font-weight: bold;")

    def onProcessButton(self):
        inputVolume = self.inputSelector.currentNode()
        if inputVolume:
            try:
                self.statusLabel.setText("正在进行CR4识别...")
                self.statusLabel.setStyleSheet("color: blue; font-weight: bold;")
                
                result = self.logic.performCR4Detection(inputVolume)
                
                if result:
                    self.statusLabel.setText("CR4识别完成")
                    self.statusLabel.setStyleSheet("color: green; font-weight: bold;")
                    slicer.util.infoDisplay("CR4关键点识别完成")
                else:
                    self.statusLabel.setText("CR4识别失败")
                    self.statusLabel.setStyleSheet("color: red; font-weight: bold;")
                    
            except Exception as e:
                slicer.util.errorDisplay(f"CR4识别失败: {e}")
                self.statusLabel.setText("处理失败")
                self.statusLabel.setStyleSheet("color: red; font-weight: bold;")

    def cleanup(self):
        pass

#
# CR4DetectionLogic
#

class CR4DetectionLogic(ScriptedLoadableModuleLogic):
    """CR4识别逻辑"""

    def __init__(self):
        ScriptedLoadableModuleLogic.__init__(self)

    def performCR4Detection(self, inputVolume):
        """执行CR4识别"""
        logging.info("开始CR4关键点识别...")
        
        try:
            # Get volume data
            volumeArray = slicer.util.arrayFromVolume(inputVolume)
            logging.info(f"Volume dimensions: {volumeArray.shape}")
            
            # Create fiducial node for landmarks
            fiducialNode = slicer.mrmlScene.AddNewNodeByClass("vtkMRMLMarkupsFiducialNode")
            fiducialNode.SetName(f"{inputVolume.GetName()}_CR4_Landmarks")
            
            # Add example landmarks (replace with actual detection)
            center = [dim/2 for dim in volumeArray.shape]
            fiducialNode.AddControlPoint([center[2], center[1], center[0]])
            
            logging.info("CR4关键点识别完成")
            return True
            
        except Exception as e:
            logging.error(f"CR4识别失败: {e}")
            return False

#
# CR4DetectionTest
#

class CR4DetectionTest(ScriptedLoadableModuleTest):
    def setUp(self):
        slicer.mrmlScene.Clear()

    def runTest(self):
        self.setUp()
        self.test_CR4Detection1()

    def test_CR4Detection1(self):
        self.delayDisplay("开始测试")
        logic = CR4DetectionLogic()
        self.delayDisplay("测试完成")
