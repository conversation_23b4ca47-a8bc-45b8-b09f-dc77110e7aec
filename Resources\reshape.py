import numpy as np
from scipy.ndimage import affine_transform
from scipy.spatial.transform import Rotation
import slicer
import vtk


def _apply_rotation_matrix(array, rotation_matrix):
    """
    使用旋转矩阵对3D数组进行旋转
    """
    # 获取数组中心点
    center = np.array(array.shape) / 2.0
    
    # 创建仿射变换矩阵
    # 先平移到原点，旋转，再平移回去
    offset = center - rotation_matrix.dot(center)
    
    # 应用仿射变换
    rotated_array = affine_transform(
        array, 
        rotation_matrix.T,  # scipy使用转置
        offset=offset,
        order=0,  # 最近邻插值，保持CT值
        mode='constant',
        cval=-1024,  # 空气的CT值
        output_shape=array.shape
    )
    
    return rotated_array


def solidify_ct_rotation_slicer(input_volume_node, rotation_matrix, output_node_name="RotatedVolume"):
    """
    固化CT旋转：根据旋转矩阵将Slicer中的Volume数据旋转后创建新的Volume节点
    使用VTK重采样方法
    
    参数:
    - input_volume_node: 输入的Slicer Volume节点
    - rotation_matrix: 3x3或4x4旋转矩阵
    - output_node_name: 输出节点名称
    
    返回:
    - 旋转后的新Volume节点
    """
    try:
        print("开始应用旋转变换到体积数据...")
        print(f"输入体积: {input_volume_node.GetName()}")
        
        # === Step 1: 处理旋转矩阵 ===
        if rotation_matrix.shape == (3, 3):
            # 扩展3x3矩阵为4x4矩阵
            transform_matrix = np.eye(4)
            transform_matrix[:3, :3] = rotation_matrix
        elif rotation_matrix.shape == (4, 4):
            transform_matrix = rotation_matrix
        else:
            raise ValueError("旋转矩阵必须是3x3或4x4的矩阵")
        
        # 获取原始体积信息
        input_spacing = input_volume_node.GetSpacing()
        input_origin = input_volume_node.GetOrigin()
        input_dimensions = input_volume_node.GetImageData().GetDimensions()
        
        print(f"原始体积信息:")
        print(f"  尺寸: {input_dimensions}")
        print(f"  间距: {input_spacing}")
        print(f"  原点: {input_origin}")
        
        # 检查变换矩阵是否为单位矩阵
        identity_check = np.allclose(transform_matrix, np.eye(4), atol=1e-6)
        print(f"变换矩阵是否为单位矩阵: {identity_check}")
        if identity_check:
            print("⚠️ 警告: 变换矩阵接近单位矩阵，可能不会产生明显变换效果")
        
        # === Step 2: 将numpy矩阵转换为VTK矩阵 ===
        vtk_matrix = vtk.vtkMatrix4x4()
        for i in range(4):
            for j in range(4):
                vtk_matrix.SetElement(i, j, float(transform_matrix[i, j]))
        
        print("VTK变换矩阵:")
        for i in range(4):
            row_str = "  ["
            for j in range(4):
                row_str += f"{vtk_matrix.GetElement(i,j):8.3f}"
                if j < 3:
                    row_str += ", "
            row_str += "]"
            print(row_str)
        
        # === Step 3: 使用VTK重采样方法 ===
        print("\n使用VTK重采样方法应用变换...")
        
        # 创建VTK变换对象
        transform = vtk.vtkTransform()
        transform.SetMatrix(vtk_matrix)
        
        # 创建重采样滤波器
        reslice = vtk.vtkImageReslice()
        reslice.SetInputData(input_volume_node.GetImageData())
        reslice.SetResliceTransform(transform)
        reslice.SetInterpolationModeToLinear()
        reslice.SetOutputDimensionality(3)
        
        # 设置输出几何信息
        reslice.SetOutputSpacing(input_spacing)
        reslice.SetOutputOrigin(input_origin)
        reslice.SetOutputExtent(0, input_dimensions[0]-1, 
                              0, input_dimensions[1]-1, 
                              0, input_dimensions[2]-1)
        
        # 执行重采样
        reslice.Update()
        
        # === Step 4: 创建新的体积节点 ===
        transformed_volume = slicer.mrmlScene.AddNewNodeByClass("vtkMRMLScalarVolumeNode")
        transformed_volume.SetName(output_node_name)
        
        # 设置变换后的图像数据
        transformed_volume.SetAndObserveImageData(reslice.GetOutput())
        
        # === Step 5: 复制几何信息 ===
        transformed_volume.SetSpacing(input_spacing)
        transformed_volume.SetOrigin(input_origin)
        
        # 复制方向矩阵
        directions = vtk.vtkMatrix4x4()
        input_volume_node.GetIJKToRASDirectionMatrix(directions)
        transformed_volume.SetIJKToRASDirectionMatrix(directions)
        
        # === Step 6: 创建并复制显示属性 ===
        transformed_volume.CreateDefaultDisplayNodes()
        if input_volume_node.GetDisplayNode():
            display_node = transformed_volume.GetDisplayNode()
            input_display_node = input_volume_node.GetDisplayNode()
            if display_node and input_display_node:
                display_node.SetWindow(input_display_node.GetWindow())
                display_node.SetLevel(input_display_node.GetLevel())
                display_node.SetAutoWindowLevel(input_display_node.GetAutoWindowLevel())
        
        # === Step 7: 验证变换结果 ===
        output_dimensions = transformed_volume.GetImageData().GetDimensions()
        print(f"变换后体积信息:")
        print(f"  尺寸: {output_dimensions}")
        print(f"  间距: {transformed_volume.GetSpacing()}")
        print(f"  原点: {transformed_volume.GetOrigin()}")
        
        print("✓ 使用VTK重采样方法成功应用变换")
        print(f"✓ 变换后体积已创建: {transformed_volume.GetName()}")
        return transformed_volume
        
    except Exception as e:
        print(f"✗ 应用旋转变换失败: {e}")
        import traceback
        traceback.print_exc()
        return None


def set_volume_as_background(volume_node):
    """
    将指定的体积节点设置为背景体积以便查看
    
    参数:
    - volume_node: 要设置为背景的体积节点
    """
    try:
        if volume_node:
            # 获取应用逻辑
            app_logic = slicer.app.applicationLogic()
            selection_node = app_logic.GetSelectionNode()
            
            # 设置为背景体积
            selection_node.SetReferenceActiveVolumeID(volume_node.GetID())
            app_logic.PropagateVolumeSelection()
            
            print(f"✓ 已将 {volume_node.GetName()} 设置为背景体积")
        else:
            print("✗ 无效的体积节点")
    except Exception as e:
        print(f"✗ 设置背景体积失败: {e}")


def get_volume_from_slicer(volume_name):
    """
    从Slicer中获取指定名称的Volume节点
    
    参数:
    - volume_name: Volume节点名称
    
    返回:
    - Volume节点，如果未找到则返回None
    """
    return slicer.util.getNode(volume_name)

def create_rotation_matrix_from_angles(rx=0, ry=0, rz=0):
    """
    从欧拉角创建旋转矩阵
    
    参数:
    - rx, ry, rz: 绕x, y, z轴的旋转角度（度）
    
    返回:
    - 3x3旋转矩阵
    """
    r = Rotation.from_euler('xyz', [rx, ry, rz], degrees=True)
    return r.as_matrix()


def create_rotation_matrix_from_axis_angle(axis, angle_degrees):
    """
    从轴角表示创建旋转矩阵
    
    参数:
    - axis: 旋转轴向量 [x, y, z]
    - angle_degrees: 旋转角度（度）
    
    返回:
    - 3x3旋转矩阵
    """
    axis = np.array(axis)
    axis = axis / np.linalg.norm(axis)  # 归一化
    r = Rotation.from_rotvec(axis * np.radians(angle_degrees))
    return r.as_matrix()


# === Slicer中的使用示例 ===
if __name__ == "__main__":
    # 示例1: 使用欧拉角创建旋转矩阵
    # 绕z轴旋转45度
    input_volume = get_volume_from_slicer("YourVolumeNodeName")  # 替换为实际的Volume节点名称
    if input_volume:
        rot_matrix = create_rotation_matrix_from_angles(rz=45)
        rotated_volume = solidify_ct_rotation_slicer(
            input_volume_node=input_volume,
            rotation_matrix=rot_matrix,
            output_node_name="RotatedVolume_45deg_Z"
        )
    
    # 示例2: 使用轴角表示创建旋转矩阵
    # 绕向量[1,1,0]旋转30度
    if input_volume:
        custom_rot_matrix = create_rotation_matrix_from_axis_angle([1, 1, 0], 30)
        rotated_volume2 = solidify_ct_rotation_slicer(
            input_volume_node=input_volume,
            rotation_matrix=custom_rot_matrix,
            output_node_name="RotatedVolume_Custom"
        )
    
    # 示例3: 直接使用旋转矩阵
    # 自定义3x3旋转矩阵
    if input_volume:
        direct_matrix = np.array([
            [0.866, -0.5, 0],
            [0.5, 0.866, 0],
            [0, 0, 1]
        ])  # 绕z轴旋转30度的矩阵
        rotated_volume3 = solidify_ct_rotation_slicer(
            input_volume_node=input_volume,
            rotation_matrix=direct_matrix,
            output_node_name="RotatedVolume_Direct"
        )


# ========== PyDICOM重写方法 (用于对比) ==========
import os
import pydicom
from pydicom.dataset import FileDataset
import datetime
import SimpleITK as sitk

def solidify_ct_rotation_dicom(input_dcm_folder, output_dcm_folder, rotation_matrix):
    """
    PyDICOM重写方法：读取DICOM文件，旋转后重写为新的DICOM文件
    
    参数:
    - input_dcm_folder: 输入DICOM文件夹路径
    - output_dcm_folder: 输出DICOM文件夹路径  
    - rotation_matrix: 3x3旋转矩阵
    """
    os.makedirs(output_dcm_folder, exist_ok=True)
    
    # === 读取DICOM序列 ===
    reader = sitk.ImageSeriesReader()
    dicom_names = reader.GetGDCMSeriesFileNames(input_dcm_folder)
    reader.SetFileNames(dicom_names)
    image = reader.Execute()
    array = sitk.GetArrayFromImage(image)  # shape: [z, y, x]
    
    # === 应用旋转 ===
    rotated_array = _apply_rotation_matrix(array, rotation_matrix)
    rotated_array = np.ascontiguousarray(rotated_array).astype(np.int16)
    
    # === 重写DICOM文件 ===
    ref_dcm = pydicom.dcmread(dicom_names[0])
    study_uid = ref_dcm.StudyInstanceUID
    series_uid = pydicom.uid.generate_uid()
    
    for i in range(rotated_array.shape[0]):
        slice_img = rotated_array[i, :, :]
        
        # 创建新的DICOM数据集
        ds = FileDataset(None, {}, file_meta=pydicom.Dataset(), preamble=b"\0" * 128)
        
        # 复制基本信息
        ds.file_meta.MediaStorageSOPClassUID = pydicom.uid.CTImageStorage
        ds.file_meta.MediaStorageSOPInstanceUID = pydicom.uid.generate_uid()
        ds.SOPClassUID = ds.file_meta.MediaStorageSOPClassUID
        ds.SOPInstanceUID = ds.file_meta.MediaStorageSOPInstanceUID
        
        # 设置序列信息
        ds.StudyInstanceUID = study_uid
        ds.SeriesInstanceUID = series_uid
        ds.InstanceNumber = i + 1
        
        # 设置像素数据
        ds.Rows, ds.Columns = slice_img.shape
        ds.PixelData = slice_img.tobytes()
        
        # 保存文件
        dcm_path = os.path.join(output_dcm_folder, f"rot_{i:04d}.dcm")
        ds.save_as(dcm_path)


# ========== 方法对比总结 ==========
"""
核心差异总结:

1. **数据持久化**
   - Slicer方法: 数据保存在Slicer场景中，关闭Slicer后数据丢失
   - PyDICOM方法: 数据永久保存为DICOM文件，可在任何DICOM查看器中打开

2. **空间精度**
   - Slicer方法: 保持完整的4x4变换矩阵，空间信息更精确
   - PyDICOM方法: 简化的空间信息，可能丢失部分精度

3. **兼容性**
   - Slicer方法: 仅限Slicer环境，但与Slicer功能完美集成
   - PyDICOM方法: 通用性强，可在任何支持DICOM的软件中使用

4. **性能**
   - Slicer方法: 内存操作，速度快
   - PyDICOM方法: 涉及文件I/O，速度相对较慢

5. **用途**
   - Slicer方法: 适合Slicer内部的快速原型和研究
   - PyDICOM方法: 适合生产环境和需要永久保存的场景

选择建议:
- 如果在Slicer中进行研究和原型开发 → 使用Slicer方法
- 如果需要生成可分发的DICOM文件 → 使用PyDICOM方法
- 如果需要与其他医学软件集成 → 使用PyDICOM方法
"""