import os
import numpy as np
import SimpleITK as sitk
import pydicom
from pydicom.dataset import FileDataset
import datetime
from scipy.ndimage import rotate, affine_transform
import matplotlib.pyplot as plt
from scipy.spatial.transform import Rotation
import warnings
warnings.filterwarnings("ignore")


def _apply_rotation_matrix(array, rotation_matrix):
    """
    使用旋转矩阵对3D数组进行旋转
    """
    # 获取数组中心点
    center = np.array(array.shape) / 2.0
    
    # 创建仿射变换矩阵
    # 先平移到原点，旋转，再平移回去
    offset = center - rotation_matrix.dot(center)
    
    # 应用仿射变换
    rotated_array = affine_transform(
        array, 
        rotation_matrix.T,  # scipy使用转置
        offset=offset,
        order=0,  # 最近邻插值，保持CT值
        mode='constant',
        cval=-1024,  # 空气的CT值
        output_shape=array.shape
    )
    
    return rotated_array


def solidify_ct_rotation(input_dcm_folder, output_dcm_folder, rotation_matrix=None, angle_degrees=None, axis=None, visualize=True):
    """
    固化CT旋转：根据旋转矩阵或角度/轴参数，将CT数据旋转后保存为新的DICOM文件
    
    参数:
    - input_dcm_folder: 输入DICOM文件夹路径
    - output_dcm_folder: 输出DICOM文件夹路径
    - rotation_matrix: 3x3旋转矩阵 (优先使用)
    - angle_degrees: 旋转角度 (当rotation_matrix为None时使用)
    - axis: 旋转轴 'x', 'y', 'z' (当rotation_matrix为None时使用)
    - visualize: 是否显示旋转前后对比图
    """
    os.makedirs(output_dcm_folder, exist_ok=True)

    # === Step 1: 读取 DICOM 原始图像 ===
    reader = sitk.ImageSeriesReader()
    dicom_names = reader.GetGDCMSeriesFileNames(input_dcm_folder)
    reader.SetFileNames(dicom_names)
    image = reader.Execute()
    array = sitk.GetArrayFromImage(image)  # shape: [z, y, x]
    old_spacing = image.GetSpacing()       # [sx, sy, sz]
    old_origin = image.GetOrigin()
    old_direction = image.GetDirection()

    # === Step 2: 处理旋转参数 ===
    if rotation_matrix is not None:
        # 使用旋转矩阵
        if rotation_matrix.shape != (3, 3):
            raise ValueError("旋转矩阵必须是3x3的矩阵")
        rotated_array = _apply_rotation_matrix(array, rotation_matrix)
        rotation_desc = "Matrix"
    else:
        # 使用角度和轴
        if angle_degrees is None or axis is None:
            raise ValueError("当不提供旋转矩阵时，必须提供angle_degrees和axis参数")
        
        # 决定旋转的轴（scipy axis 顺序为：z=0, y=1, x=2）
        if axis == 'x':
            axes = (1, 0)  # rotate y-z
        elif axis == 'y':
            axes = (2, 0)  # rotate x-z
        elif axis == 'z':
            axes = (2, 1)  # rotate x-y
        else:
            raise ValueError("axis must be one of ['x', 'y', 'z']")
        
        # 执行旋转
        rotated_array = rotate(array, angle=angle_degrees, axes=axes, reshape=True, order=0, mode='constant', cval=-1024)
        rotation_desc = f"{angle_degrees}° ({axis}-axis)"
    
    rotated_array = np.ascontiguousarray(rotated_array).astype(np.int16)

    # === Step 3: 可视化旋转前后图像中间切片 ===
    if visualize:
        mid_idx = array.shape[0] // 2
        mid_idx_rot = rotated_array.shape[0] // 2
        plt.figure(figsize=(10, 5))
        plt.subplot(1, 2, 1)
        plt.imshow(array[mid_idx], cmap='gray')
        plt.title("旋转前")
        plt.axis("off")
        plt.subplot(1, 2, 2)
        plt.imshow(rotated_array[mid_idx_rot], cmap='gray')
        plt.title(f"旋转后 ({rotation_desc})")
        plt.axis("off")
        plt.tight_layout()
        plt.show()

    # === Step 5: 准备保存每一层 DICOM ===
    ref_dcm = pydicom.dcmread(dicom_names[0])
    study_uid = ref_dcm.StudyInstanceUID
    series_uid = pydicom.uid.generate_uid()
    study_date = datetime.datetime.now().strftime("%Y%m%d")
    study_time = datetime.datetime.now().strftime("%H%M%S")

    # === Step 4: 处理spacing和origin ===
    if rotation_matrix is not None:
        # 使用旋转矩阵时，保持原始spacing
        new_spacing = list(old_spacing)
    else:
        # 使用角度轴旋转时，根据轴调整spacing
        if axis == 'x':
            new_spacing = [old_spacing[1], old_spacing[2], old_spacing[0]]
        elif axis == 'y':
            new_spacing = [old_spacing[0], old_spacing[1], old_spacing[2]]
        elif axis == 'z':
            new_spacing = [old_spacing[2], old_spacing[0], old_spacing[1]]

    # === Step 7: 保存每一层为 DICOM ===
    for i in range(rotated_array.shape[0]):
        slice_img = rotated_array[i, :, :]
        ds = FileDataset(None, {}, file_meta=pydicom.Dataset(), preamble=b"\0" * 128)
        ds.file_meta.MediaStorageSOPClassUID = pydicom.uid.CTImageStorage
        ds.file_meta.MediaStorageSOPInstanceUID = pydicom.uid.generate_uid()
        ds.file_meta.ImplementationClassUID = pydicom.uid.PYDICOM_IMPLEMENTATION_UID

        ds.SOPClassUID = ds.file_meta.MediaStorageSOPClassUID
        ds.SOPInstanceUID = ds.file_meta.MediaStorageSOPInstanceUID
        ds.StudyInstanceUID = study_uid
        ds.SeriesInstanceUID = series_uid
        ds.Modality = "CT"
        ds.SeriesNumber = 1
        ds.InstanceNumber = i + 1
        ds.ImageType = ['ORIGINAL', 'PRIMARY', 'AXIAL']
        ds.PatientID = ref_dcm.get("PatientID", "Unknown")
        ds.PatientName = ref_dcm.get("PatientName", "Unknown")
        ds.StudyDate = study_date
        ds.StudyTime = study_time
        ds.ContentDate = study_date
        ds.ContentTime = study_time
        ds.Manufacturer = "RotatedByPython"

        # 空间信息
        ds.Rows, ds.Columns = slice_img.shape
        ds.PixelSpacing = [str(new_spacing[1]), str(new_spacing[2])]
        ds.SliceThickness = str(new_spacing[0])
        ds.SpacingBetweenSlices = str(new_spacing[0])

        # 方向矩阵
        ds.ImageOrientationPatient = ['1', '0', '0', '0', '1', '0']  # 暂默认
        # 位置
        ds.ImagePositionPatient = [str(old_origin[0]), str(old_origin[1]), str(old_origin[2] + i * float(new_spacing[0]))]

        # 像素值相关
        ds.SamplesPerPixel = 1
        ds.PhotometricInterpretation = "MONOCHROME2"
        ds.BitsAllocated = 16
        ds.BitsStored = 16
        ds.HighBit = 15
        ds.PixelRepresentation = 1
        ds.PixelData = slice_img.tobytes()

        # 写出文件
        dcm_path = os.path.join(output_dcm_folder, f"rot_{i:04d}.dcm")
        ds.save_as(dcm_path)

    print(f"成功保存 {rotated_array.shape[0]} 层旋转后的 DICOM 到: {output_dcm_folder}")

def create_rotation_matrix_from_angles(rx=0, ry=0, rz=0):
    """
    从欧拉角创建旋转矩阵
    
    参数:
    - rx, ry, rz: 绕x, y, z轴的旋转角度（度）
    
    返回:
    - 3x3旋转矩阵
    """
    r = Rotation.from_euler('xyz', [rx, ry, rz], degrees=True)
    return r.as_matrix()


def create_rotation_matrix_from_axis_angle(axis, angle_degrees):
    """
    从轴角表示创建旋转矩阵
    
    参数:
    - axis: 旋转轴向量 [x, y, z]
    - angle_degrees: 旋转角度（度）
    
    返回:
    - 3x3旋转矩阵
    """
    axis = np.array(axis)
    axis = axis / np.linalg.norm(axis)  # 归一化
    r = Rotation.from_rotvec(axis * np.radians(angle_degrees))
    return r.as_matrix()


# === 使用示例 ===
if __name__ == "__main__":
    # 示例1: 使用角度和轴进行旋转
    solidify_ct_rotation(
        input_dcm_folder=r"C:\Users\<USER>\Desktop\1",
        output_dcm_folder=r"C:\Users\<USER>\Desktop\2_angle",
        angle_degrees=180,
        axis='z',
        visualize=False
    )
    
    # 示例2: 使用旋转矩阵进行旋转
    # 创建一个绕z轴旋转45度的矩阵
    rot_matrix = create_rotation_matrix_from_angles(rz=45)
    solidify_ct_rotation(
        input_dcm_folder=r"C:\Users\<USER>\Desktop\1",
        output_dcm_folder=r"C:\Users\<USER>\Desktop\2_matrix",
        rotation_matrix=rot_matrix,
        visualize=False
    )
    
    # 示例3: 使用自定义轴角旋转
    # 绕向量[1,1,0]旋转30度
    custom_rot_matrix = create_rotation_matrix_from_axis_angle([1, 1, 0], 30)
    solidify_ct_rotation(
        input_dcm_folder=r"C:\Users\<USER>\Desktop\1",
        output_dcm_folder=r"C:\Users\<USER>\Desktop\2_custom",
        rotation_matrix=custom_rot_matrix,
        visualize=True
    )

