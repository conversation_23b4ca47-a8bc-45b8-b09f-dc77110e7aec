"""
切片平面变换工具 - 简化版本
只保留读取角度功能
"""

import logging
import vtk
import qt
import ctk
import slicer
import numpy as np
try:
    from scipy.ndimage import affine_transform
    SCIPY_AVAILABLE = True
except ImportError:
    print("⚠️ scipy不可用，将使用VTK重采样方法")
    SCIPY_AVAILABLE = False

class SlicePlaneTransformBasicWidget(qt.QWidget):
    """切片平面变换控制面板 - 简化版本"""
    
    transformChanged = qt.Signal()
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.lastComputedMatrices = None  # 存储最后计算的矩阵数据
        self.setupUI()
        
    def setupUI(self):
        """设置用户界面"""
        layout = qt.QVBoxLayout(self)
        
        # 标题
        titleLabel = qt.QLabel("切片平面变换工具")
        titleLabel.setStyleSheet("font-size: 14px; font-weight: bold; color: #2E8B57;")
        layout.addWidget(titleLabel)
        
        # 按钮布局
        buttonLayout = qt.QHBoxLayout()

        # 切片功能切换按钮
        self.enableSliceButton = qt.QPushButton("启用切片")
        self.enableSliceButton.setToolTip("启用/关闭切片交叉线功能")
        self.enableSliceButton.clicked.connect(self.toggleSliceInteraction)
        self.enableSliceButton.setStyleSheet("QPushButton { min-height: 30px; font-size: 12px; }")
        buttonLayout.addWidget(self.enableSliceButton)

        # 添加切片状态跟踪
        self.sliceIntersectionEnabled = False

        # 重置切面按钮
        self.resetSlicesButton = qt.QPushButton("重置切面")
        self.resetSlicesButton.setToolTip("将红、黄、绿三个切片重置为单位矩阵（默认位置）")
        self.resetSlicesButton.clicked.connect(self.resetSlicesToIdentity)
        self.resetSlicesButton.setStyleSheet("QPushButton { min-height: 30px; font-size: 12px; }")
        buttonLayout.addWidget(self.resetSlicesButton)

        # 调整头位按钮
        self.createTransformButton = qt.QPushButton("调整头位")
        self.createTransformButton.setToolTip("一键读取切片角度并自动应用头位调整到CT数据")
        self.createTransformButton.clicked.connect(self.createTransformNode)
        self.createTransformButton.setStyleSheet("QPushButton { min-height: 30px; font-size: 12px; }")
        buttonLayout.addWidget(self.createTransformButton)

        layout.addLayout(buttonLayout)

        # 第二行按钮布局 - 重采样功能
        resampleButtonLayout = qt.QHBoxLayout()

        # 摆正头位重采样按钮
        self.resampleButton = qt.QPushButton("摆正头位(重采样)")
        self.resampleButton.setToolTip("使用VTK重采样方法创建摆正头位的新数据")
        self.resampleButton.clicked.connect(self.resampleHeadPosition)
        self.resampleButton.setStyleSheet("QPushButton { min-height: 30px; font-size: 12px; background-color: #4CAF50; color: white; }")
        resampleButtonLayout.addWidget(self.resampleButton)

        layout.addLayout(resampleButtonLayout)

        # 添加ROI功能区域
        self.setupROISection(layout)

        # 状态显示
        self.statusLabel = qt.QLabel("就绪")
        self.statusLabel.setStyleSheet("color: green; font-style: italic;")
        layout.addWidget(self.statusLabel)
        
        print("✓ 简化版切片变换工具UI已创建")

    def toggleSliceInteraction(self):
        """切换Slice intersections功能的启用/关闭"""
        try:
            layoutManager = slicer.app.layoutManager()
            if not layoutManager:
                print("⚠️ 未找到 LayoutManager")
                return

            # 切换状态
            self.sliceIntersectionEnabled = not self.sliceIntersectionEnabled

            if self.sliceIntersectionEnabled:
                self.enableSliceInteraction()
            else:
                self.disableSliceInteraction()

        except Exception as e:
            print(f"切换Slice intersections失败: {e}")
            self.statusLabel.setText("切换Slice intersections失败")
            self.statusLabel.setStyleSheet("color: red; font-style: italic;")
            import traceback
            traceback.print_exc()

    def enableSliceInteraction(self):
        """启用Slice intersections功能"""
        try:
            print("启用Slice intersections功能...")

            layoutManager = slicer.app.layoutManager()
            if not layoutManager:
                print("⚠️ 未找到 LayoutManager")
                return

            # 切换到四视图布局，确保多个切片视图可见（否则无交点显示）
            layoutManager.setLayout(slicer.vtkMRMLLayoutNode.SlicerLayoutFourUpView)
            print("✓ 已切换到四视图布局")

            # 确保有数据加载到场景中
            self.ensureVolumeDataLoaded()

            # 首先重置所有切片视图，确保它们显示数据
            self.resetAndFitAllSliceViews()

            # 等待视图更新
            slicer.app.processEvents()

            for sliceViewName in layoutManager.sliceViewNames():
                sliceWidget = layoutManager.sliceWidget(sliceViewName)
                if not sliceWidget:
                    print(f"⚠️ 未找到切片控件: {sliceViewName}")
                    continue

                sliceLogic = sliceWidget.sliceLogic()
                sliceNode = sliceLogic.GetSliceNode()
                sliceDisplayNode = sliceLogic.GetSliceDisplayNode()

                if not sliceDisplayNode:
                    print(f"⚠️ {sliceViewName} 没有找到对应的 SliceDisplayNode")
                    continue

                # 启用切片交点可见性
                sliceDisplayNode.SetIntersectingSlicesVisibility(True)

                # 启用交互（拖拽、旋转、平移）
                sliceDisplayNode.SetIntersectingSlicesInteractive(True)
                sliceDisplayNode.SetIntersectingSlicesRotationEnabled(True)
                sliceDisplayNode.SetIntersectingSlicesTranslationEnabled(True)

                # 设置线粗细（0=细, 1=中, 2=粗）
                sliceDisplayNode.SetIntersectingSlicesLineThicknessMode(1)

                # 设置交点模式（0=SkipLineCrossings, 1=FullLines；默认1，不跳过交叉）
                sliceDisplayNode.SetIntersectingSlicesIntersectionMode(1)

                # 强制更新节点和视图，确保立即显示
                sliceDisplayNode.Modified()
                sliceNode.Modified()

                # 确保切片适配到数据
                sliceLogic.FitSliceToAll()

                # 强制渲染
                sliceWidget.sliceView().forceRender()
                sliceWidget.sliceView().scheduleRender()

                print(f"✓ 已启用 {sliceViewName} Slice intersections（含交互）")

            # 额外的全局刷新
            layoutManager.resetSliceViews()

            # 强制处理所有待处理的事件
            slicer.app.processEvents()

            # 更新按钮和状态
            self.enableSliceButton.setText("关闭切片")
            self.statusLabel.setText("Slice intersections已启用（含交互）")
            self.statusLabel.setStyleSheet("color: blue; font-style: italic;")
            print("✓ Slice intersections功能已启用（含交互）")

        except Exception as e:
            print(f"启用Slice intersections失败: {e}")
            self.statusLabel.setText("启用Slice intersections失败")
            self.statusLabel.setStyleSheet("color: red; font-style: italic;")
            import traceback
            traceback.print_exc()

    def disableSliceInteraction(self):
        """关闭Slice intersections功能"""
        try:
            print("关闭Slice intersections功能...")

            layoutManager = slicer.app.layoutManager()
            if not layoutManager:
                print("⚠️ 未找到 LayoutManager")
                return

            for sliceViewName in layoutManager.sliceViewNames():
                sliceWidget = layoutManager.sliceWidget(sliceViewName)
                if not sliceWidget:
                    print(f"⚠️ 未找到切片控件: {sliceViewName}")
                    continue

                sliceLogic = sliceWidget.sliceLogic()
                sliceNode = sliceLogic.GetSliceNode()
                sliceDisplayNode = sliceLogic.GetSliceDisplayNode()

                if not sliceDisplayNode:
                    print(f"⚠️ {sliceViewName} 没有找到对应的 SliceDisplayNode")
                    continue

                # 关闭切片交点可见性
                sliceDisplayNode.SetIntersectingSlicesVisibility(False)

                # 关闭交互功能
                sliceDisplayNode.SetIntersectingSlicesInteractive(False)
                sliceDisplayNode.SetIntersectingSlicesRotationEnabled(False)
                sliceDisplayNode.SetIntersectingSlicesTranslationEnabled(False)

                # 强制更新节点和视图
                sliceDisplayNode.Modified()
                sliceNode.Modified()

                # 强制渲染
                sliceWidget.sliceView().forceRender()
                sliceWidget.sliceView().scheduleRender()

                print(f"✓ 已关闭 {sliceViewName} Slice intersections")

            # 额外的全局刷新
            layoutManager.resetSliceViews()

            # 强制刷新3D视图
            threeDWidget = layoutManager.threeDWidget(0)
            if threeDWidget:
                threeDView = threeDWidget.threeDView()
                if threeDView:
                    threeDView.forceRender()

            # 强制处理所有待处理的事件
            slicer.app.processEvents()

            # 更新按钮和状态
            self.enableSliceButton.setText("启用切片")
            self.statusLabel.setText("Slice intersections已关闭")
            self.statusLabel.setStyleSheet("color: gray; font-style: italic;")
            print("✓ Slice intersections功能已关闭")

        except Exception as e:
            print(f"关闭Slice intersections失败: {e}")
            self.statusLabel.setText("关闭Slice intersections失败")
            self.statusLabel.setStyleSheet("color: red; font-style: italic;")
            import traceback
            traceback.print_exc()

    def ensureVolumeDataLoaded(self):
        """确保有体积数据加载到场景中"""
        try:
            # 检查是否有体积数据
            volumeNodes = slicer.util.getNodesByClass('vtkMRMLScalarVolumeNode')
            if not volumeNodes:
                print("⚠️ 场景中没有找到体积数据")
                return False

            # 获取当前选择的体积或第一个体积
            currentVolume = None
            if hasattr(self, 'mainWidget') and hasattr(self.mainWidget, 'inputSelector'):
                currentVolume = self.mainWidget.inputSelector.currentNode()

            if not currentVolume and volumeNodes:
                currentVolume = volumeNodes[0]

            if currentVolume:
                print(f"✓ 使用体积数据: {currentVolume.GetName()}")

                # 确保体积在所有切片视图中显示
                compositeNodes = slicer.util.getNodesByClass('vtkMRMLSliceCompositeNode')
                for compositeNode in compositeNodes:
                    compositeNode.SetBackgroundVolumeID(currentVolume.GetID())

                return True
            else:
                print("⚠️ 没有可用的体积数据")
                return False

        except Exception as e:
            print(f"确保体积数据加载失败: {e}")
            return False

    def resetAndFitAllSliceViews(self):
        """重置并适配所有切片视图"""
        try:
            print("重置并适配所有切片视图...")

            layoutManager = slicer.app.layoutManager()
            if not layoutManager:
                return

            # 重置所有切片视图
            layoutManager.resetSliceViews()

            # 对每个切片视图进行适配
            for sliceViewName in layoutManager.sliceViewNames():
                sliceWidget = layoutManager.sliceWidget(sliceViewName)
                if sliceWidget:
                    sliceLogic = sliceWidget.sliceLogic()
                    if sliceLogic:
                        # 适配切片到所有数据
                        sliceLogic.FitSliceToAll()
                        print(f"✓ 已适配 {sliceViewName} 切片视图")

                    # 强制渲染
                    sliceView = sliceWidget.sliceView()
                    if sliceView:
                        sliceView.forceRender()

            print("✓ 所有切片视图已重置并适配")

        except Exception as e:
            print(f"重置切片视图失败: {e}")

    def setupROISection(self, layout):
        """设置ROI功能区域"""
        # ROI创建区域
        roiGroupBox = qt.QGroupBox("ROI创建 (自动居中到数据中心)")
        layout.addWidget(roiGroupBox)
        roiLayout = qt.QFormLayout(roiGroupBox)

        # ROI名称输入
        self.roiNameEdit = qt.QLineEdit()
        self.roiNameEdit.setText("ROI_1")
        self.roiNameEdit.setToolTip("输入ROI名称")
        roiLayout.addRow("ROI名称:", self.roiNameEdit)

        # ROI尺寸设置
        sizeLayout = qt.QHBoxLayout()

        # 长度
        self.roiLengthSpinBox = qt.QDoubleSpinBox()
        self.roiLengthSpinBox.setRange(1.0, 500.0)
        self.roiLengthSpinBox.setValue(50.0)
        self.roiLengthSpinBox.setSuffix(" mm")
        self.roiLengthSpinBox.setToolTip("ROI长度")
        sizeLayout.addWidget(qt.QLabel("长:"))
        sizeLayout.addWidget(self.roiLengthSpinBox)

        # 宽度
        self.roiWidthSpinBox = qt.QDoubleSpinBox()
        self.roiWidthSpinBox.setRange(1.0, 500.0)
        self.roiWidthSpinBox.setValue(50.0)
        self.roiWidthSpinBox.setSuffix(" mm")
        self.roiWidthSpinBox.setToolTip("ROI宽度")
        sizeLayout.addWidget(qt.QLabel("宽:"))
        sizeLayout.addWidget(self.roiWidthSpinBox)

        # 高度
        self.roiHeightSpinBox = qt.QDoubleSpinBox()
        self.roiHeightSpinBox.setRange(1.0, 500.0)
        self.roiHeightSpinBox.setValue(50.0)
        self.roiHeightSpinBox.setSuffix(" mm")
        self.roiHeightSpinBox.setToolTip("ROI高度")
        sizeLayout.addWidget(qt.QLabel("高:"))
        sizeLayout.addWidget(self.roiHeightSpinBox)

        roiLayout.addRow("尺寸设置:", sizeLayout)

        # 连接尺寸控件的信号，实现双向同步
        self.roiLengthSpinBox.valueChanged.connect(self.onROISizeChanged)
        self.roiWidthSpinBox.valueChanged.connect(self.onROISizeChanged)
        self.roiHeightSpinBox.valueChanged.connect(self.onROISizeChanged)

        # 添加说明文本
        infoLabel = qt.QLabel("ROI将自动创建在CT数据的几何中心位置")
        infoLabel.setStyleSheet("color: #666; font-style: italic; margin: 5px;")
        roiLayout.addRow(infoLabel)

        # ROI操作按钮布局 - 四个按钮一排
        roiButtonLayout = qt.QHBoxLayout()

        # ROI创建按钮
        self.createROIButton = qt.QPushButton("创建ROI")
        self.createROIButton.setToolTip("在CT数据中心创建新的ROI")
        self.createROIButton.clicked.connect(self.onCreateROI)
        self.createROIButton.setStyleSheet("QPushButton { min-height: 30px; }")
        roiButtonLayout.addWidget(self.createROIButton)

        # ROI显示/隐藏按钮
        self.toggleROIButton = qt.QPushButton("显示ROI")
        self.toggleROIButton.setToolTip("显示或隐藏所有ROI")
        self.toggleROIButton.clicked.connect(self.onToggleROIVisibility)
        self.toggleROIButton.setStyleSheet("QPushButton { min-height: 30px; }")
        roiButtonLayout.addWidget(self.toggleROIButton)

        # ROI清理按钮
        self.clearROIButton = qt.QPushButton("清理ROI")
        self.clearROIButton.setToolTip("删除所有ROI节点")
        self.clearROIButton.clicked.connect(self.onClearROI)
        self.clearROIButton.setStyleSheet("QPushButton { min-height: 30px; }")
        roiButtonLayout.addWidget(self.clearROIButton)

        # ROI裁切按钮
        self.cropVolumeButton = qt.QPushButton("使用ROI裁切数据")
        self.cropVolumeButton.setToolTip("使用ROI裁切当前选择的CT数据")
        self.cropVolumeButton.clicked.connect(self.onCropVolumeWithROI)
        self.cropVolumeButton.setStyleSheet("QPushButton { min-height: 30px; }")
        roiButtonLayout.addWidget(self.cropVolumeButton)

        roiLayout.addRow(roiButtonLayout)

    def quickReadSliceAngles(self):
        """快速读取红、黄、绿三个切片的当前角度和矩阵"""
        try:
            print("\n" + "="*50)
            print("快速读取切片变换数据（矩阵+角度）")
            print("="*50)
            
            angles_data = {}
            
            # 读取三个主要切片的角度
            for sliceName in ['Red', 'Yellow', 'Green']:
                slice_info = self.readSliceTransformData(sliceName)
                if slice_info and 'euler_angles_deg' in slice_info:
                    angles = slice_info['euler_angles_deg']
                    translation = slice_info.get('translation', [0, 0, 0])
                    matrix = slice_info.get('matrix')
                    
                    angles_data[sliceName] = {
                        'rotation': {
                            'X': round(angles[0], 2),
                            'Y': round(angles[1], 2), 
                            'Z': round(angles[2], 2)
                        },
                        'translation': {
                            'X': round(translation[0], 2),
                            'Y': round(translation[1], 2),
                            'Z': round(translation[2], 2)
                        },
                        'matrix': matrix
                    }
                    
                    slice_type = {'Red': '轴状面', 'Yellow': '矢状面', 'Green': '冠状面'}[sliceName]
                    print(f"\n{sliceName} 切片 ({slice_type}):")
                    
                    # 显示变换矩阵
                    print("  变换矩阵 (SliceToRAS):")
                    for i in range(4):
                        row_str = "    ["
                        for j in range(4):
                            row_str += f"{matrix[i,j]:8.3f}"
                            if j < 3:
                                row_str += ", "
                        row_str += "]"
                        print(row_str)
                    
                    # 显示角度和位移
                    print(f"  旋转角度: X={angles[0]:.2f}°, Y={angles[1]:.2f}°, Z={angles[2]:.2f}°")
                    print(f"  平移距离: X={translation[0]:.2f}mm, Y={translation[1]:.2f}mm, Z={translation[2]:.2f}mm")
                else:
                    print(f"\n✗ 无法读取 {sliceName} 切片数据")
            
            # 计算等效变换矩阵和逆矩阵
            if len(angles_data) == 3 and 'Red' in angles_data and 'Yellow' in angles_data and 'Green' in angles_data:
                print("\n" + "-"*50)
                print("计算等效变换矩阵和逆矩阵")
                print("-"*50)
                
                red_matrix = angles_data['Red']['matrix']
                yellow_matrix = angles_data['Yellow']['matrix']
                green_matrix = angles_data['Green']['matrix']
                
                result_matrices = self.create_matrix_from_slice_normals(red_matrix, green_matrix, yellow_matrix)
                
                if result_matrices:
                    equivalent_matrix = result_matrices["equivalent_transform_matrix"]
                    straightening_matrix = result_matrices["straightening_matrix (inverse)"]
                    
                    print("\n等效变换矩阵 (Equivalent Transform Matrix):")
                    for i in range(4):
                        row_str = "  ["
                        for j in range(4):
                            row_str += f"{equivalent_matrix[i,j]:8.3f}"
                            if j < 3:
                                row_str += ", "
                        row_str += "]"
                        print(row_str)
                    
                    print("\n矫正矩阵/逆矩阵 (Straightening Matrix - Inverse):")
                    for i in range(4):
                        row_str = "  ["
                        for j in range(4):
                            row_str += f"{straightening_matrix[i,j]:8.3f}"
                            if j < 3:
                                row_str += ", "
                        row_str += "]"
                        print(row_str)
                    
                    # 将结果添加到返回数据中
                    angles_data['computed_matrices'] = result_matrices
                    # 存储最后计算的矩阵数据供创建Transform节点使用
                    self.lastComputedMatrices = result_matrices
                    print("\n✓ 等效矩阵和逆矩阵计算完成")
                else:
                    print("\n✗ 等效矩阵计算失败")
            
            # 更新状态
            if angles_data:
                self.statusLabel.setText(f"已读取 {len(angles_data)} 个切片的变换数据")
                print(f"\n✓ 成功读取 {len(angles_data)} 个切片的变换数据")
            else:
                self.statusLabel.setText("未能读取任何切片变换数据")
                print("\n✗ 未能读取任何切片变换数据")
            
            print("="*50)
            return angles_data
            
        except Exception as e:
            print(f"\n✗ 快速读取变换数据失败: {e}")
            self.statusLabel.setText("读取变换数据失败")
            return {}
    
    def readSliceTransformData(self, sliceName):
        """读取切片变换数据"""
        try:
            # 获取切片节点
            sliceWidget = slicer.app.layoutManager().sliceWidget(sliceName)
            if not sliceWidget:
                return None
                
            sliceNode = sliceWidget.mrmlSliceNode()
            if not sliceNode:
                return None
            
            # 获取切片到RAS的变换矩阵
            sliceToRAS = sliceNode.GetSliceToRAS()
            
            # 转换为numpy数组
            matrix_np = np.zeros((4, 4))
            for i in range(4):
                for j in range(4):
                    matrix_np[i, j] = sliceToRAS.GetElement(i, j)
            
            # 提取旋转矩阵和平移向量
            rotation_matrix = matrix_np[:3, :3]
            translation_vector = matrix_np[:3, 3]
            
            # 将旋转矩阵转换为欧拉角
            euler_angles_rad = self.rotationMatrixToEulerAngles(rotation_matrix)
            euler_angles_deg = np.degrees(euler_angles_rad)
            
            return {
                'matrix': matrix_np,
                'rotation_matrix': rotation_matrix,
                'translation': translation_vector.tolist(),
                'euler_angles_rad': euler_angles_rad.tolist(),
                'euler_angles_deg': euler_angles_deg.tolist()
            }
            
        except Exception as e:
            print(f"读取切片 {sliceName} 变换数据失败: {e}")
            return None
    
    def rotationMatrixToEulerAngles(self, R):
        """将旋转矩阵转换为欧拉角（XYZ顺序）"""
        try:
            # 检查是否为有效的旋转矩阵
            if R.shape != (3, 3):
                raise ValueError("旋转矩阵必须是3x3")
            
            # 计算欧拉角（XYZ顺序）
            sy = np.sqrt(R[0, 0] * R[0, 0] + R[1, 0] * R[1, 0])
            
            singular = sy < 1e-6
            
            if not singular:
                x = np.arctan2(R[2, 1], R[2, 2])
                y = np.arctan2(-R[2, 0], sy)
                z = np.arctan2(R[1, 0], R[0, 0])
            else:
                x = np.arctan2(-R[1, 2], R[1, 1])
                y = np.arctan2(-R[2, 0], sy)
                z = 0
            
            return np.array([x, y, z])
            
        except Exception as e:
            print(f"旋转矩阵转换欧拉角失败: {e}")
            return np.array([0, 0, 0])
    
    def create_matrix_from_slice_normals(self, red_matrix, green_matrix, yellow_matrix):
        """从切片法向量创建等效变换矩阵和逆矩阵"""
        try:
            # 提取各切片的法向量（第3列，即Z轴方向）
            new_x_axis = yellow_matrix[:3, 2]  # Yellow切片法向量作为新X轴
            new_y_axis = green_matrix[:3, 2]   # Green切片法向量作为新Y轴  
            new_z_axis = red_matrix[:3, 2]     # Red切片法向量作为新Z轴
            
            # 构建原始旋转矩阵
            raw_rotation_matrix = np.stack([new_x_axis, new_y_axis, new_z_axis], axis=1)
            
            # 使用SVD分解获得纯旋转矩阵
            try:
                U, S, Vh = np.linalg.svd(raw_rotation_matrix)
                pure_rotation_matrix = U @ Vh
            except np.linalg.LinAlgError:
                print("✗ SVD分解失败，无法计算纯旋转矩阵")
                return None
            
            # 创建等效变换矩阵
            equivalent_matrix = np.identity(4)
            equivalent_matrix[:3, :3] = pure_rotation_matrix
            
            # 创建逆变换矩阵（矫正矩阵）
            straightening_matrix = np.identity(4)
            straightening_matrix[:3, :3] = pure_rotation_matrix.T
            
            return {
                "equivalent_transform_matrix": equivalent_matrix,
                "straightening_matrix (inverse)": straightening_matrix
            }
            
        except Exception as e:
            print(f"创建等效矩阵失败: {e}")
            return None
    
    def resetSlicesToIdentity(self):
        """将红、黄、绿三个切片重置为单位矩阵（默认位置）"""
        try:
            print("\n" + "="*50)
            print("重置切片为单位矩阵")
            print("="*50)
            
            reset_count = 0
            
            # 重置三个主要切片
            for sliceName in ['Red', 'Yellow', 'Green']:
                if self.resetSingleSliceToIdentity(sliceName):
                    reset_count += 1
                    slice_type = {'Red': '轴状面', 'Yellow': '矢状面', 'Green': '冠状面'}[sliceName]
                    print(f"✓ {sliceName} 切片 ({slice_type}) 已重置为单位矩阵")
                else:
                    print(f"✗ {sliceName} 切片重置失败")
            
            # 更新状态
            if reset_count > 0:
                self.statusLabel.setText(f"已重置 {reset_count} 个切片为单位矩阵")
                print(f"\n✓ 成功重置 {reset_count} 个切片为单位矩阵")
                
                # 刷新视图
                slicer.app.layoutManager().resetSliceViews()
                print("✓ 切片视图已刷新")
            else:
                self.statusLabel.setText("切片重置失败")
                print("\n✗ 所有切片重置失败")
            
            print("="*50)
            return reset_count > 0
            
        except Exception as e:
            print(f"\n✗ 重置切片失败: {e}")
            self.statusLabel.setText("重置切片失败")
            return False
    
    def resetSingleSliceToIdentity(self, sliceName):
        """重置单个切片为单位矩阵"""
        try:
            # 获取切片节点
            sliceWidget = slicer.app.layoutManager().sliceWidget(sliceName)
            if not sliceWidget:
                return False
                
            sliceNode = sliceWidget.mrmlSliceNode()
            if not sliceNode:
                return False
            
            # 使用正确的方法重置切片方向
            # 方法1：重置切片方向为默认
            sliceNode.SetOrientationToDefault()
            
            # 方法2：或者直接设置为标准方向
            if sliceName == 'Red':
                sliceNode.SetOrientationToAxial()
            elif sliceName == 'Yellow':
                sliceNode.SetOrientationToSagittal()
            elif sliceName == 'Green':
                sliceNode.SetOrientationToCoronal()
            
            # 重置切片偏移为0
            sliceNode.SetSliceOffset(0.0)
            
            # 更新切片视图
            sliceWidget.sliceLogic().FitSliceToAll()
            
            return True
            
        except Exception as e:
            print(f"重置切片 {sliceName} 失败: {e}")
            return False
    
    def createTransformNode(self):
        """简化版头位调整 - 直接创建Transform节点并保持"""
        try:
            print("\n" + "="*50)
            print("简化版头位调整")
            print("="*50)
            
            # 获取HU伪彩模块中选择的CT数据
            ctNode = None
            if hasattr(self, 'mainWidget') and hasattr(self.mainWidget, 'inputSelector'):
                ctNode = self.mainWidget.inputSelector.currentNode()
                if ctNode:
                    print(f"✓ 使用HU伪彩模块选择的CT数据: {ctNode.GetName()}")
                else:
                    print("✗ HU伪彩模块中未选择CT数据")
                    self.statusLabel.setText("请先在HU伪彩模块中选择CT数据")
                    return False
            else:
                print("✗ 无法访问HU伪彩模块的输入选择器")
                self.statusLabel.setText("无法访问主模块输入选择器")
                return False
            
            # 读取切片角度
            angles_data = self.quickReadSliceAngles()
            if not angles_data or 'computed_matrices' not in angles_data:
                print("✗ 无法读取切片数据")
                self.statusLabel.setText("读取切片数据失败")
                return False
            
            # 获取矫正矩阵
            straightening_matrix = angles_data['computed_matrices'].get("straightening_matrix (inverse)")
            if straightening_matrix is None:
                print("✗ 无法获取矫正矩阵")
                self.statusLabel.setText("矫正矩阵不可用")
                return False
            
            print("应用的矫正矩阵:")
            for i in range(4):
                row_str = "  ["
                for j in range(4):
                    row_str += f"{straightening_matrix[i,j]:8.3f}"
                    if j < 3:
                        row_str += ", "
                row_str += "]"
                print(row_str)
            
            # 删除已存在的头位调整变换
            existingTransforms = slicer.util.getNodesByClass('vtkMRMLLinearTransformNode')
            for transform in existingTransforms:
                if "头位调整" in transform.GetName():
                    slicer.mrmlScene.RemoveNode(transform)
                    print("✓ 删除已存在的头位调整变换")
            
            # 创建新的Transform节点
            transformNode = slicer.mrmlScene.AddNewNodeByClass("vtkMRMLLinearTransformNode")
            transformNode.SetName("头位调整变换")
            
            # 设置变换矩阵
            vtkMatrix = vtk.vtkMatrix4x4()
            for i in range(4):
                for j in range(4):
                    vtkMatrix.SetElement(i, j, float(straightening_matrix[i, j]))
            
            transformNode.SetMatrixTransformToParent(vtkMatrix)
            print("✓ Transform节点已创建")
            
            # 将CT数据应用到Transform节点
            ctNode.SetAndObserveTransformNodeID(transformNode.GetID())
            print("✓ CT数据已应用变换")
            
            # 使用改进的硬化变换方法，更可靠
            print("正在应用变换到CT数据...")
            success = self.improvedHardenTransform(ctNode, transformNode)

            if success:
                print("✓ 变换已成功应用到CT数据")
                print("✓ Transform节点已删除")
                print("✓ 数据已完全固化，不依赖任何变换矩阵")
                self.statusLabel.setText("头位调整完成 - 变换已固化")

                # 确保主模块中的输入选择器仍然指向正确的数据
                if hasattr(self, 'mainWidget') and hasattr(self.mainWidget, 'inputSelector'):
                    self.mainWidget.inputSelector.setCurrentNode(ctNode)
                    print("✓ 已更新主模块输入选择器")
            else:
                print("✗ 变换应用失败，保持Transform节点")
                self.statusLabel.setText("头位调整完成 - Transform节点已应用")
            
            # 强制更新场景和视图
            slicer.mrmlScene.Modified()

            # 适配切片到体积
            layoutManager = slicer.app.layoutManager()
            for sliceName in ['Red', 'Yellow', 'Green']:
                sliceWidget = layoutManager.sliceWidget(sliceName)
                if sliceWidget:
                    sliceLogic = sliceWidget.sliceLogic()
                    if sliceLogic:
                        sliceLogic.FitSliceToAll()
                    sliceView = sliceWidget.sliceView()
                    if sliceView:
                        sliceView.scheduleRender()
            
            print("✓ 头位调整完成！变换已永久应用到CT数据")
            print("="*50)
            
            return True
            
        except Exception as e:
            print(f"✗ 头位调整失败: {e}")
            self.statusLabel.setText("头位调整失败")
            import traceback
            traceback.print_exc()
            return False

    def resampleHeadPosition(self):
        """使用改进的重采样方法摆正头位"""
        try:
            print("\n" + "="*50)
            print("摆正头位 - 改进重采样方法")
            print("="*50)

            # 获取HU伪彩模块中选择的CT数据
            ctNode = None
            if hasattr(self, 'mainWidget') and hasattr(self.mainWidget, 'inputSelector'):
                ctNode = self.mainWidget.inputSelector.currentNode()
                if ctNode:
                    print(f"✓ 使用HU伪彩模块选择的CT数据: {ctNode.GetName()}")
                else:
                    print("✗ HU伪彩模块中未选择CT数据")
                    self.statusLabel.setText("请先在HU伪彩模块中选择CT数据")
                    return False
            else:
                print("✗ 无法访问HU伪彩模块的输入选择器")
                self.statusLabel.setText("无法访问主模块输入选择器")
                return False

            # 验证输入数据
            imageData = ctNode.GetImageData()
            if not imageData or imageData.GetNumberOfPoints() == 0:
                print("✗ 输入CT数据为空")
                self.statusLabel.setText("输入CT数据为空")
                return False

            print(f"输入数据验证通过，包含 {imageData.GetNumberOfPoints()} 个点")

            # 读取切片角度
            angles_data = self.quickReadSliceAngles()
            if not angles_data or 'computed_matrices' not in angles_data:
                print("✗ 无法读取切片数据")
                self.statusLabel.setText("读取切片数据失败")
                return False

            # 获取矫正矩阵
            straightening_matrix = angles_data['computed_matrices'].get("straightening_matrix (inverse)")
            if straightening_matrix is None:
                print("✗ 无法获取矫正矩阵")
                self.statusLabel.setText("矫正矩阵不可用")
                return False

            print("应用的矫正矩阵:")
            for i in range(4):
                row_str = "  ["
                for j in range(4):
                    row_str += f"{straightening_matrix[i,j]:8.3f}"
                    if j < 3:
                        row_str += ", "
                row_str += "]"
                print(row_str)

            # 使用改进的重采样方法
            resampledVolume = self.improvedResampleVolume(ctNode, straightening_matrix)

            if resampledVolume:
                print("✓ 摆正头位重采样完成")
                self.statusLabel.setText("摆正头位重采样完成")
                self.statusLabel.setStyleSheet("color: green; font-style: italic;")

                # 更新主模块输入选择器指向新的重采样数据
                if hasattr(self, 'mainWidget') and hasattr(self.mainWidget, 'inputSelector'):
                    self.mainWidget.inputSelector.setCurrentNode(resampledVolume)
                    print("✓ 已更新主模块输入选择器指向重采样数据")

                print("✓ 摆正头位重采样完成！新数据已创建并设置为当前数据")
                print("="*50)
                return True
            else:
                print("✗ 摆正头位重采样失败")
                self.statusLabel.setText("摆正头位重采样失败")
                self.statusLabel.setStyleSheet("color: red; font-style: italic;")
                return False

        except Exception as e:
            print(f"✗ 摆正头位重采样失败: {e}")
            self.statusLabel.setText("摆正头位重采样失败")
            self.statusLabel.setStyleSheet("color: red; font-style: italic;")
            import traceback
            traceback.print_exc()
            return False

    def improvedResampleVolume(self, inputVolume, transformMatrix):
        """改进的重采样方法，参考rotate_dicom_series的思路"""
        try:
            print("开始改进的重采样方法...")
            print(f"输入体积: {inputVolume.GetName()}")

            # 获取原始体积的图像数据
            imageData = inputVolume.GetImageData()
            if not imageData or imageData.GetNumberOfPoints() == 0:
                print("✗ 输入图像数据为空")
                return None

            # 获取原始体积信息
            dimensions = imageData.GetDimensions()
            spacing = inputVolume.GetSpacing()
            origin = inputVolume.GetOrigin()

            print(f"原始体积信息:")
            print(f"  尺寸: {dimensions}")
            print(f"  间距: {spacing}")
            print(f"  原点: {origin}")

            # 将VTK图像数据转换为numpy数组
            from vtk.util import numpy_support
            vtk_array = imageData.GetPointData().GetScalars()
            numpy_array = numpy_support.vtk_to_numpy(vtk_array)

            # 重塑为3D数组 (VTK数据布局是 x*y*z 的线性排列)
            # VTK的数据顺序是 (x, y, z)，需要重塑为 (z, y, x) 以便处理
            numpy_array = numpy_array.reshape(dimensions[2], dimensions[1], dimensions[0])
            # 转置为标准的 (z, y, x) 顺序
            numpy_array = np.transpose(numpy_array, (0, 1, 2))

            print(f"转换后的numpy数组形状: {numpy_array.shape}")
            print(f"数据类型: {numpy_array.dtype}")
            print(f"数值范围: [{numpy_array.min()}, {numpy_array.max()}]")

            # 检查scipy是否可用
            if not SCIPY_AVAILABLE:
                print("✗ scipy不可用，回退到VTK重采样方法")
                return self.applyTransformToVolume(inputVolume, transformMatrix)

            # 使用scipy的affine_transform进行变换

            # 提取旋转矩阵（3x3）
            rotation_matrix = transformMatrix[:3, :3]
            translation = transformMatrix[:3, 3]

            print("旋转矩阵:")
            for i in range(3):
                print(f"  [{rotation_matrix[i,0]:8.3f}, {rotation_matrix[i,1]:8.3f}, {rotation_matrix[i,2]:8.3f}]")
            print(f"平移向量: [{translation[0]:8.3f}, {translation[1]:8.3f}, {translation[2]:8.3f}]")

            # 计算输出数组的中心
            center = np.array(numpy_array.shape) / 2.0
            print(f"数组中心: {center}")

            # 创建仿射变换矩阵（scipy使用逆变换）
            # 先平移到中心，然后旋转，再平移回去
            inv_rotation = np.linalg.inv(rotation_matrix)
            print("逆旋转矩阵:")
            for i in range(3):
                print(f"  [{inv_rotation[i,0]:8.3f}, {inv_rotation[i,1]:8.3f}, {inv_rotation[i,2]:8.3f}]")

            # 计算偏移量：center - inv_rotation @ center
            offset = center - np.dot(inv_rotation, center)
            print(f"偏移量: {offset}")

            print("执行仿射变换...")

            # 执行变换，使用线性插值
            transformed_array = affine_transform(
                numpy_array,
                matrix=inv_rotation,
                offset=offset,
                output_shape=numpy_array.shape,
                order=1,  # 线性插值
                mode='constant',
                cval=-1024,  # CT的空气HU值
                prefilter=False
            )

            # 检查变换结果
            non_fill_pixels = np.sum(transformed_array != -1024)
            total_pixels = transformed_array.size
            print(f"变换结果检查: {non_fill_pixels}/{total_pixels} ({non_fill_pixels/total_pixels*100:.1f}%) 非填充像素")

            print(f"变换后数组形状: {transformed_array.shape}")
            print(f"变换后数值范围: [{transformed_array.min()}, {transformed_array.max()}]")

            # 确保数据类型正确
            if numpy_array.dtype == np.int16:
                transformed_array = transformed_array.astype(np.int16)
            elif numpy_array.dtype == np.float32:
                transformed_array = transformed_array.astype(np.float32)

            # 创建新的VTK图像数据
            newImageData = vtk.vtkImageData()
            newImageData.SetDimensions(dimensions)
            newImageData.SetSpacing(spacing)
            newImageData.SetOrigin(origin)

            # 将numpy数组转换回VTK格式
            # VTK期望数据按 (x, y, z) 顺序排列，所以需要转置
            # 从 (z, y, x) 转换为 (x, y, z) 然后展平
            vtk_ordered_array = np.transpose(transformed_array, (2, 1, 0))
            flattened_array = vtk_ordered_array.ravel(order='F')  # Fortran顺序，与VTK兼容

            print(f"VTK数组转换: 形状 {vtk_ordered_array.shape} -> 展平长度 {len(flattened_array)}")

            vtk_data_array = numpy_support.numpy_to_vtk(
                flattened_array,
                deep=True,
                array_type=vtk.VTK_SHORT if transformed_array.dtype == np.int16 else vtk.VTK_FLOAT
            )

            # 设置数组名称
            vtk_data_array.SetName("ImageScalars")
            newImageData.GetPointData().SetScalars(vtk_data_array)

            # 强制更新VTK数据
            newImageData.Modified()

            # 创建新的体积节点
            resampledVolume = slicer.mrmlScene.AddNewNodeByClass("vtkMRMLScalarVolumeNode")
            resampledVolume.SetName(f"{inputVolume.GetName()}_Resampled")
            resampledVolume.SetAndObserveImageData(newImageData)

            # 复制几何信息
            resampledVolume.SetSpacing(spacing)
            resampledVolume.SetOrigin(origin)

            # 复制方向矩阵
            directions = vtk.vtkMatrix4x4()
            inputVolume.GetIJKToRASDirectionMatrix(directions)
            resampledVolume.SetIJKToRASDirectionMatrix(directions)

            # 创建并复制显示属性
            resampledVolume.CreateDefaultDisplayNodes()
            if inputVolume.GetDisplayNode():
                displayNode = resampledVolume.GetDisplayNode()
                inputDisplayNode = inputVolume.GetDisplayNode()
                if displayNode and inputDisplayNode:
                    displayNode.SetWindow(inputDisplayNode.GetWindow())
                    displayNode.SetLevel(inputDisplayNode.GetLevel())
                    displayNode.SetAutoWindowLevel(inputDisplayNode.GetAutoWindowLevel())

            # 验证结果
            resultImageData = resampledVolume.GetImageData()
            if resultImageData and resultImageData.GetNumberOfPoints() > 0:
                print(f"✓ 改进重采样成功，新体积包含 {resultImageData.GetNumberOfPoints()} 个点")

                # 验证数据内容
                resultScalars = resultImageData.GetPointData().GetScalars()
                if resultScalars:
                    resultArray = numpy_support.vtk_to_numpy(resultScalars)
                    print(f"结果数据验证: 长度={len(resultArray)}, 范围=[{resultArray.min()}, {resultArray.max()}]")

                    # 检查是否有非填充值的数据
                    non_fill_count = np.sum(resultArray != -1024)
                    print(f"非填充值数据点数: {non_fill_count} ({non_fill_count/len(resultArray)*100:.1f}%)")

                    if non_fill_count == 0:
                        print("⚠️ 警告: 所有数据都是填充值，可能变换有问题")
                else:
                    print("⚠️ 警告: 无法获取结果数据的标量值")

                # 强制更新显示节点
                displayNode = resampledVolume.GetDisplayNode()
                if displayNode:
                    displayNode.SetAutoWindowLevel(True)
                    displayNode.Modified()
                    print("✓ 已更新显示节点")

                # 设置为当前背景体积
                self.setVolumeAsBackground(resampledVolume)

                return resampledVolume
            else:
                print("✗ 改进重采样失败，结果数据为空")
                return None

        except Exception as e:
            print(f"✗ 改进重采样失败: {e}")
            import traceback
            traceback.print_exc()
            return None

    def improvedHardenTransform(self, volumeNode, transformNode):
        """改进的硬化变换方法，更可靠地应用变换"""
        try:
            print("开始改进的硬化变换...")

            # 首先验证输入数据
            inputImageData = volumeNode.GetImageData()
            if not inputImageData or inputImageData.GetNumberOfPoints() == 0:
                print("❌ 输入数据为空或无效")
                return False

            print(f"输入数据验证通过，包含 {inputImageData.GetNumberOfPoints()} 个点")

            # 使用Slicer的Transforms模块逻辑硬化变换
            transformsLogic = slicer.modules.transforms.logic()
            if not transformsLogic:
                print("❌ Transforms模块逻辑不可用")
                return False

            print("使用Transforms模块逻辑硬化变换...")

            # 验证变换前的数据
            beforeImageData = volumeNode.GetImageData()
            beforePoints = beforeImageData.GetNumberOfPoints()
            print(f"变换前数据点数: {beforePoints}")

            # 执行硬化变换
            transformsLogic.hardenTransform(volumeNode)

            # 验证变换后的数据
            afterImageData = volumeNode.GetImageData()
            if afterImageData and afterImageData.GetNumberOfPoints() > 0:
                afterPoints = afterImageData.GetNumberOfPoints()
                print(f"✓ 硬化变换成功，变换后数据点数: {afterPoints}")

                # 检查数据点数是否一致
                if afterPoints == beforePoints:
                    print("✓ 数据完整性验证通过")
                else:
                    print(f"⚠️ 数据点数发生变化: {beforePoints} -> {afterPoints}")

                # 删除Transform节点
                slicer.mrmlScene.RemoveNode(transformNode)
                print("✓ Transform节点已删除")

                return True
            else:
                print("❌ 硬化变换后数据为空")
                return False

        except Exception as e:
            print(f"❌ 改进硬化变换失败: {e}")
            import traceback
            traceback.print_exc()
            return False


    def applyTransformToVolume(self, inputVolume, transformMatrix):
        """使用VTK重采样方法直接应用变换矩阵到体积数据，创建新的变换后体积"""
        try:
            print("开始使用VTK重采样方法应用变换到体积数据...")
            print(f"输入体积: {inputVolume.GetName()}")

            # 获取原始体积信息
            inputSpacing = inputVolume.GetSpacing()
            inputOrigin = inputVolume.GetOrigin()
            inputDimensions = inputVolume.GetImageData().GetDimensions()

            print(f"原始体积信息:")
            print(f"  尺寸: {inputDimensions}")
            print(f"  间距: {inputSpacing}")
            print(f"  原点: {inputOrigin}")

            # 检查变换矩阵是否为单位矩阵
            identity_check = np.allclose(transformMatrix, np.eye(4), atol=1e-6)
            print(f"变换矩阵是否为单位矩阵: {identity_check}")

            if identity_check:
                print("⚠️ 警告: 变换矩阵接近单位矩阵，可能不会产生明显变换效果")

            # 将numpy矩阵转换为VTK矩阵
            vtkMatrix = vtk.vtkMatrix4x4()
            for i in range(4):
                for j in range(4):
                    vtkMatrix.SetElement(i, j, float(transformMatrix[i, j]))

            print("VTK变换矩阵:")
            for i in range(4):
                row_str = "  ["
                for j in range(4):
                    row_str += f"{vtkMatrix.GetElement(i,j):8.3f}"
                    if j < 3:
                        row_str += ", "
                row_str += "]"
                print(row_str)

            # 使用VTK重采样方法
            print("\n使用VTK重采样方法应用变换...")

            # 创建VTK变换对象
            transform = vtk.vtkTransform()
            transform.SetMatrix(vtkMatrix)

            # 创建重采样滤波器
            reslice = vtk.vtkImageReslice()
            reslice.SetInputData(inputVolume.GetImageData())
            reslice.SetResliceTransform(transform)
            reslice.SetInterpolationModeToLinear()
            reslice.SetOutputDimensionality(3)

            # 设置输出几何信息
            reslice.SetOutputSpacing(inputSpacing)
            reslice.SetOutputOrigin(inputOrigin)
            reslice.SetOutputExtent(0, inputDimensions[0]-1,
                                   0, inputDimensions[1]-1,
                                   0, inputDimensions[2]-1)

            # 执行重采样
            print("执行VTK重采样...")
            reslice.Update()

            # 创建新的体积节点来存储变换后的结果
            transformedVolume = slicer.mrmlScene.AddNewNodeByClass("vtkMRMLScalarVolumeNode")
            transformedVolume.SetName(f"{inputVolume.GetName()}_Resampled")

            # 设置变换后的图像数据
            transformedVolume.SetAndObserveImageData(reslice.GetOutput())

            # 复制几何信息
            transformedVolume.SetSpacing(inputSpacing)
            transformedVolume.SetOrigin(inputOrigin)

            # 复制方向矩阵
            directions = vtk.vtkMatrix4x4()
            inputVolume.GetIJKToRASDirectionMatrix(directions)
            transformedVolume.SetIJKToRASDirectionMatrix(directions)

            # 创建并复制显示属性
            transformedVolume.CreateDefaultDisplayNodes()
            if inputVolume.GetDisplayNode():
                displayNode = transformedVolume.GetDisplayNode()
                inputDisplayNode = inputVolume.GetDisplayNode()
                if displayNode and inputDisplayNode:
                    displayNode.SetWindow(inputDisplayNode.GetWindow())
                    displayNode.SetLevel(inputDisplayNode.GetLevel())
                    displayNode.SetAutoWindowLevel(inputDisplayNode.GetAutoWindowLevel())

            # 验证变换是否成功
            outputDimensions = transformedVolume.GetImageData().GetDimensions()
            print(f"变换后体积信息:")
            print(f"  尺寸: {outputDimensions}")
            print(f"  间距: {transformedVolume.GetSpacing()}")
            print(f"  原点: {transformedVolume.GetOrigin()}")

            # 设置为当前背景体积以便查看
            self.setVolumeAsBackground(transformedVolume)

            print(f"✓ VTK重采样变换完成，新体积已创建: {transformedVolume.GetName()}")
            return transformedVolume

        except Exception as e:
            print(f"✗ VTK重采样变换失败: {e}")
            import traceback
            traceback.print_exc()
            return None
    
    def setVolumeAsBackground(self, volume):
        """将体积设置为所有切片视图的背景"""
        try:
            print(f"设置体积为背景: {volume.GetName()}")
            
            # 获取所有切片复合节点
            compositeNodes = slicer.util.getNodesByClass('vtkMRMLSliceCompositeNode')
            for compositeNode in compositeNodes:
                compositeNode.SetBackgroundVolumeID(volume.GetID())
                print(f"✓ 已设置 {compositeNode.GetName()} 的背景体积")
            
            # 适配切片到体积
            layoutManager = slicer.app.layoutManager()
            if layoutManager:
                for sliceName in ['Red', 'Yellow', 'Green']:
                    sliceWidget = layoutManager.sliceWidget(sliceName)
                    if sliceWidget:
                        sliceLogic = sliceWidget.sliceLogic()
                        if sliceLogic:
                            sliceLogic.FitSliceToAll()
                            print(f"✓ 已适配 {sliceName} 切片")
            
            print("✓ 体积已设置为背景并适配切片")
            
        except Exception as e:
            print(f"✗ 设置背景体积失败: {e}")

    # ROI相关方法
    def onROISizeChanged(self):
        """ROI尺寸改变时的处理"""
        # 这里可以添加实时更新ROI的逻辑
        pass

    def onCreateROI(self):
        """创建ROI"""
        try:
            from Resources.ROIManager import onCreateROI
            onCreateROI(self)
        except Exception as e:
            print(f"创建ROI失败: {e}")

    def onToggleROIVisibility(self):
        """切换ROI可见性"""
        try:
            from Resources.ROIManager import onToggleROIVisibility
            onToggleROIVisibility(self)
        except Exception as e:
            print(f"切换ROI可见性失败: {e}")

    def onClearROI(self):
        """清理ROI"""
        try:
            from Resources.ROIManager import clearOldROIs
            clearOldROIs()
        except Exception as e:
            print(f"清理ROI失败: {e}")

    def onCropVolumeWithROI(self):
        """使用ROI裁切数据"""
        try:
            from Resources.ROIManager import onCropVolumeWithROI
            onCropVolumeWithROI(self)
        except Exception as e:
            print(f"ROI裁切失败: {e}")

def addBasicSlicePlaneTransformToWidget(widget):
    """添加简化版本的切片平面变换工具到指定widget"""
    try:
        print("开始添加简化切片平面变换工具...")
        
        # 创建可折叠按钮
        transformCollapsibleButton = ctk.ctkCollapsibleButton()
        transformCollapsibleButton.text = "切片平面变换工具"
        transformCollapsibleButton.collapsed = False
        widget.layout.addWidget(transformCollapsibleButton)
        print("✓ 可折叠按钮已创建")
        
        # 创建简化变换工具
        basicWidget = SlicePlaneTransformBasicWidget()
        
        # 设置主模块引用，以便访问输入选择器
        basicWidget.mainWidget = widget

        # 将ROI相关的属性传递给变换控件，使其能够访问输入选择器
        if hasattr(widget, 'inputSelector'):
            basicWidget.inputSelector = widget.inputSelector
            print("✓ 已设置输入选择器引用")

        transformLayout = qt.QVBoxLayout(transformCollapsibleButton)
        transformLayout.addWidget(basicWidget)

        print("✓ 简化切片平面变换工具添加成功")
        print(f"✓ 已设置主模块引用: {hasattr(basicWidget, 'mainWidget')}")

        return basicWidget
        
    except Exception as e:
        print(f"添加简化版本失败: {e}")
        return None